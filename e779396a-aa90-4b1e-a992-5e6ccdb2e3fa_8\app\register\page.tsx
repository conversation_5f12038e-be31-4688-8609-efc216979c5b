
'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 清除对应字段的错误信息
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.username.trim()) {
      newErrors.username = '请输入用户名';
    }

    if (!formData.email.trim()) {
      newErrors.email = '请输入邮箱';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = '邮箱格式不正确';
    }

    if (!formData.password) {
      newErrors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      newErrors.password = '密码长度至少6位';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认密码';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // 这里后续可以添加注册逻辑
      console.log('注册数据:', formData);
    }
  };

  return (
    <div className="relative min-h-screen overflow-hidden bg-black flex items-center justify-center">
      {/* 增强版星空背景效果 */}
      <div className="absolute inset-0">
        {/* 主要螺旋星系背景 - 增大尺寸 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="relative w-[1000px] h-[1000px] animate-spin opacity-25" style={{ animationDuration: '-75s' }}>
            {/* 多层螺旋环 - 增大尺寸 */}
            <div className="absolute inset-0">
              <div className="absolute w-full h-full rounded-full border-3 border-purple-400/20 animate-pulse"></div>
              <div className="absolute w-4/5 h-4/5 top-[10%] left-[10%] rounded-full border-3 border-pink-400/25 animate-pulse" style={{ animationDelay: '1.5s' }}></div>
              <div className="absolute w-3/5 h-3/5 top-[20%] left-[20%] rounded-full border-3 border-cyan-400/30 animate-pulse" style={{ animationDelay: '3s' }}></div>
              <div className="absolute w-2/5 h-2/5 top-[30%] left-[30%] rounded-full border-3 border-blue-400/35 animate-pulse" style={{ animationDelay: '4.5s' }}></div>
              <div className="absolute w-1/5 h-1/5 top-[40%] left-[40%] rounded-full border-2 border-yellow-400/40 animate-pulse" style={{ animationDelay: '6s' }}></div>
            </div>
            
            {/* 螺旋粒子 - 增加数量和调整分布 */}
            {Array.from({ length: 120 }).map((_, i) => {
              const angle = (i * 10) % 360;
              const radius = 100 + (i * 4);
              const x = Math.cos((angle * Math.PI) / 180) * radius;
              const y = Math.sin((angle * Math.PI) / 180) * radius;
              return (
                <div
                  key={i}
                  className="absolute w-1.5 h-1.5 bg-white rounded-full animate-pulse"
                  style={{
                    left: `calc(50% + ${x}px)`,
                    top: `calc(50% + ${y}px)`,
                    animationDelay: `${i * 0.06}s`,
                    opacity: 0.2 + ((i % 4) * 0.15),
                    boxShadow: `0 0 ${3 + (i % 4)}px ${['#ff00ff', '#00ffff', '#ffff00', '#ff6b6b'][i % 4]}`
                  }}
                />
              );
            })}
          </div>
        </div>

        {/* 第二层反向旋转螺旋 - 增大尺寸 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="relative w-[700px] h-[700px] animate-spin opacity-20" style={{ animationDuration: '60s' }}>
            {Array.from({ length: 60 }).map((_, i) => {
              const angle = (i * 15) % 360;
              const radius = 50 + (i * 6);
              const x = Math.cos((angle * Math.PI) / 180) * radius;
              const y = Math.sin((angle * Math.PI) / 180) * radius;
              return (
                <div
                  key={i}
                  className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-600 rounded-full animate-pulse"
                  style={{
                    left: `calc(50% + ${x}px)`,
                    top: `calc(50% + ${y}px)`,
                    animationDelay: `${i * 0.12}s`,
                    opacity: 0.3 + ((i % 3) * 0.2),
                    boxShadow: `0 0 4px #E879F9`
                  }}
                />
              );
            })}
          </div>
        </div>

        {/* 增强的流动星尘 */}
        {Array.from({ length: 180 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-px h-px bg-white rounded-full animate-pulse"
            style={{
              left: `${(i * 8) % 100}%`,
              top: `${(i * 13) % 100}%`,
              animationDelay: `${(i % 18) * 0.25}s`,
              animationDuration: `${2.5 + (i % 4) * 0.5}s`,
              opacity: 0.15 + ((i % 5) * 0.12)
            }}
          />
        ))}

        {/* 大型光晕粒子 - 增大尺寸 */}
        {Array.from({ length: 30 }).map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full animate-pulse"
            style={{
              left: `${(i * 15) % 100}%`,
              top: `${(i * 19) % 100}%`,
              width: `${5 + (i % 5)}px`,
              height: `${5 + (i % 5)}px`,
              background: `radial-gradient(circle, ${['#ff00ff', '#00ffff', '#ffff00', '#ff6b6b', '#9333ea'][i % 5]} 0%, transparent 70%)`,
              animationDelay: `${i * 0.3}s`,
              animationDuration: `${4 + (i % 3)}s`
            }}
          />
        ))}

        {/* 超级流星雨 */}
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1.5 h-1.5 bg-gradient-to-r from-white via-purple-300 to-transparent rounded-full"
            style={{
              left: `${(i * 9) % 100}%`,
              top: `${(i * 21) % 100}%`,
              animation: `meteor ${2.5 + (i % 4) * 0.5}s linear infinite`,
              animationDelay: `${i * 0.3}s`,
              transform: 'rotate(-45deg)',
              boxShadow: '0 0 20px 2px rgba(255,255,255,0.7), 0 0 40px 3px rgba(147,51,234,0.4)',
            }}
          />
        ))}
        
        {/* 多层渐变覆盖 */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/12 via-pink-900/18 to-black/45" />
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/25 to-black/65" />
      </div>

      {/* 注册表单容器 */}
      <div className="relative z-10 w-full max-w-md mx-4">
        {/* 返回首页按钮 */}
        <Link 
          href="/" 
          className="inline-flex items-center text-cyan-400 hover:text-cyan-300 transition-colors duration-300 mb-8 group cursor-pointer"
        >
          <i className="ri-arrow-left-line mr-2 text-xl w-6 h-6 flex items-center justify-center group-hover:-translate-x-1 transition-transform duration-300"></i>
          返回首页
        </Link>

        {/* 注册卡片 */}
        <div className="bg-gray-900/65 backdrop-blur-md border border-gray-700/60 rounded-2xl p-8 shadow-2xl relative overflow-hidden">
          {/* 卡片内部光效 - 增大尺寸 */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-400/6 via-transparent to-pink-400/6 pointer-events-none"></div>
          <div className="absolute -top-32 -right-32 w-64 h-64 bg-gradient-radial from-purple-400/12 to-transparent rounded-full animate-pulse"></div>
          <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-gradient-radial from-pink-400/12 to-transparent rounded-full animate-pulse" style={{ animationDelay: '1.5s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gradient-conic from-cyan-400/8 via-purple-400/8 to-pink-400/8 rounded-full animate-spin" style={{ animationDuration: '20s' }}></div>
          
          <div className="relative z-10">
            {/* 标题 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-500 mb-2 font-['Pacifico'] animate-pulse">
                创建新账户
              </h1>
              <p className="text-gray-300">加入星际论坛大家庭</p>
            </div>

            {/* 注册表单 */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 用户名输入框 */}
              <div className="group">
                <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2 group-focus-within:text-purple-400 transition-colors duration-300">
                  用户名
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="ri-user-line text-gray-400 w-5 h-5 flex items-center justify-center group-focus-within:text-purple-400 transition-colors duration-300"></i>
                  </div>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 text-sm backdrop-blur-sm ${
                      errors.username 
                        ? 'border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50' 
                        : 'border-gray-600/50 focus:ring-purple-400/50 focus:border-purple-400/50'
                    }`}
                    placeholder="请输入用户名"
                    required
                  />
                </div>
                {errors.username && (
                  <p className="mt-1 text-sm text-red-400 animate-pulse">{errors.username}</p>
                )}
              </div>

              {/* 邮箱输入框 */}
              <div className="group">
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2 group-focus-within:text-purple-400 transition-colors duration-300">
                  邮箱
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="ri-mail-line text-gray-400 w-5 h-5 flex items-center justify-center group-focus-within:text-purple-400 transition-colors duration-300"></i>
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 text-sm backdrop-blur-sm ${
                      errors.email 
                        ? 'border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50' 
                        : 'border-gray-600/50 focus:ring-purple-400/50 focus:border-purple-400/50'
                    }`}
                    placeholder="请输入邮箱地址"
                    required
                  />
                </div>
                {errors.email && (
                  <p className="mt-1 text-sm text-red-400 animate-pulse">{errors.email}</p>
                )}
              </div>

              {/* 密码输入框 */}
              <div className="group">
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2 group-focus-within:text-purple-400 transition-colors duration-300">
                  密码
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="ri-lock-line text-gray-400 w-5 h-5 flex items-center justify-center group-focus-within:text-purple-400 transition-colors duration-300"></i>
                  </div>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 text-sm backdrop-blur-sm ${
                      errors.password 
                        ? 'border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50' 
                        : 'border-gray-600/50 focus:ring-purple-400/50 focus:border-purple-400/50'
                    }`}
                    placeholder="请输入密码（至少6位）"
                    required
                  />
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-400 animate-pulse">{errors.password}</p>
                )}
              </div>

              {/* 确认密码输入框 */}
              <div className="group">
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-2 group-focus-within:text-purple-400 transition-colors duration-300">
                  确认密码
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="ri-lock-2-line text-gray-400 w-5 h-5 flex items-center justify-center group-focus-within:text-purple-400 transition-colors duration-300"></i>
                  </div>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`w-full pl-10 pr-4 py-3 bg-gray-800/50 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 text-sm backdrop-blur-sm ${
                      errors.confirmPassword 
                        ? 'border-red-500/50 focus:ring-red-400/50 focus:border-red-400/50' 
                        : 'border-gray-600/50 focus:ring-purple-400/50 focus:border-purple-400/50'
                    }`}
                    placeholder="请再次输入密码"
                    required
                  />
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-400 animate-pulse">{errors.confirmPassword}</p>
                )}
              </div>

              {/* 注册按钮 */}
              <button
                type="submit"
                className="w-full py-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg text-white font-semibold text-lg hover:from-purple-600 hover:to-pink-700 transform hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/40 whitespace-nowrap cursor-pointer relative overflow-hidden group"
              >
                <span className="relative z-10 flex items-center justify-center">
                  <i className="ri-user-add-line mr-2 w-5 h-5 inline-flex items-center justify-center"></i>
                  注册
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-purple-700 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </button>
            </form>

            {/* 登录链接 */}
            <div className="mt-8 text-center">
              <p className="text-gray-300">
                已有账户？
                <Link 
                  href="/login" 
                  className="text-cyan-400 hover:text-cyan-300 transition-colors duration-300 ml-2 cursor-pointer hover:underline"
                >
                  点击这里登录
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CSS 动画定义 */}
      <style jsx>{`
        @keyframes meteor {
          0% {
            transform: rotate(-45deg) translateX(-100px);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: rotate(-45deg) translateX(calc(100vw + 100px));
            opacity: 0;
          }
        }
        
        .bg-gradient-radial {
          background: radial-gradient(circle, var(--tw-gradient-stops));
        }
        
        .bg-gradient-conic {
          background: conic-gradient(var(--tw-gradient-stops));
        }
      `}</style>
    </div>
  );
}