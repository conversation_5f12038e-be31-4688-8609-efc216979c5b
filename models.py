import sqlite3
import json
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from config import Config

class Database:
    """数据库操作类"""
    
    def __init__(self, db_path=None):
        self.db_path = db_path or Config.DATABASE
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """初始化数据库表和数据"""
        conn = self.get_connection()
        
        # 创建学校表
        conn.execute('''
            CREATE TABLE IF NOT EXISTS schools (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建用户表
        conn.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                school_id INTEGER NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (school_id) REFERENCES schools(id)
            )
        ''')
        
        # 创建帖子表
        conn.execute('''
            CREATE TABLE IF NOT EXISTS posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(200) NOT NULL,
                content TEXT NOT NULL,
                user_id INTEGER NOT NULL,
                school_id INTEGER NOT NULL,
                images TEXT,
                view_count INTEGER DEFAULT 0,
                comment_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (school_id) REFERENCES schools(id)
            )
        ''')
        
        # 创建评论表
        conn.execute('''
            CREATE TABLE IF NOT EXISTS comments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                content TEXT NOT NULL,
                post_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                parent_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (parent_id) REFERENCES comments(id)
            )
        ''')
        
        # 创建索引
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_schools_name ON schools(name)',
            'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
            'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
            'CREATE INDEX IF NOT EXISTS idx_users_school_id ON users(school_id)',
            'CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_posts_school_id ON posts(school_id)',
            'CREATE INDEX IF NOT EXISTS idx_posts_created_at ON posts(created_at DESC)',
            'CREATE INDEX IF NOT EXISTS idx_posts_view_count ON posts(view_count DESC)',
            'CREATE INDEX IF NOT EXISTS idx_comments_post_id ON comments(post_id)',
            'CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id)'
        ]
        
        for index in indexes:
            conn.execute(index)
        
        # 初始化学校数据
        schools = [
            ('北京大学', '北京大学官方论坛板块'),
            ('清华大学', '清华大学官方论坛板块'),
            ('复旦大学', '复旦大学官方论坛板块'),
            ('上海交通大学', '上海交通大学官方论坛板块')
        ]
        
        for school in schools:
            try:
                conn.execute('INSERT INTO schools (name, description) VALUES (?, ?)', school)
            except sqlite3.IntegrityError:
                pass  # 学校已存在，跳过
        
        conn.commit()
        conn.close()

class School:
    """学校模型类"""
    
    def __init__(self, db):
        self.db = db
    
    def get_all(self):
        """获取所有学校"""
        conn = self.db.get_connection()
        schools = conn.execute('SELECT * FROM schools ORDER BY name').fetchall()
        conn.close()
        return schools
    
    def get_by_id(self, school_id):
        """根据ID获取学校"""
        conn = self.db.get_connection()
        school = conn.execute('SELECT * FROM schools WHERE id = ?', (school_id,)).fetchone()
        conn.close()
        return school
    
    def get_by_province(self, province):
        """根据省份获取学校列表"""
        conn = self.db.get_connection()
        schools = conn.execute('SELECT * FROM schools WHERE province = ? ORDER BY name', (province,)).fetchall()
        conn.close()
        return schools
    
    def get_by_city(self, city):
        """根据城市获取学校列表"""
        conn = self.db.get_connection()
        schools = conn.execute('SELECT * FROM schools WHERE city = ? ORDER BY name', (city,)).fetchall()
        conn.close()
        return schools
    
    def get_by_type(self, school_type):
        """根据学校类型获取学校列表"""
        conn = self.db.get_connection()
        schools = conn.execute('SELECT * FROM schools WHERE school_type = ? ORDER BY name', (school_type,)).fetchall()
        conn.close()
        return schools
    
    def get_provinces(self):
        """获取所有省份列表"""
        conn = self.db.get_connection()
        provinces = conn.execute('SELECT DISTINCT province FROM schools WHERE province IS NOT NULL ORDER BY province').fetchall()
        conn.close()
        return [row['province'] for row in provinces]
    
    def get_cities_by_province(self, province):
        """根据省份获取城市列表"""
        conn = self.db.get_connection()
        cities = conn.execute('SELECT DISTINCT city FROM schools WHERE province = ? AND city IS NOT NULL ORDER BY city', (province,)).fetchall()
        conn.close()
        return [row['city'] for row in cities]
    
    def get_school_types(self):
        """获取所有学校类型列表"""
        conn = self.db.get_connection()
        types = conn.execute('SELECT DISTINCT school_type FROM schools WHERE school_type IS NOT NULL ORDER BY school_type').fetchall()
        conn.close()
        return [row['school_type'] for row in types]
    
    def search_schools(self, keyword, province=None, city=None, school_type=None, limit=50):
        """搜索学校"""
        conn = self.db.get_connection()
        
        sql = 'SELECT * FROM schools WHERE (name LIKE ? OR description LIKE ?)'
        params = [f'%{keyword}%', f'%{keyword}%']
        
        if province:
            sql += ' AND province = ?'
            params.append(province)
        
        if city:
            sql += ' AND city = ?'
            params.append(city)
        
        if school_type:
            sql += ' AND school_type = ?'
            params.append(school_type)
        
        sql += ' ORDER BY name LIMIT ?'
        params.append(limit)
        
        schools = conn.execute(sql, params).fetchall()
        conn.close()
        return schools
    
    def create(self, name, description='', province=None, city=None, school_type=None, level=None, website=None, address=None, phone=None, logo_url=None):
        """创建新学校"""
        conn = self.db.get_connection()
        try:
            cursor = conn.execute('''
                INSERT INTO schools (name, description, province, city, school_type, level, website, address, phone, logo_url) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, description, province, city, school_type, level, website, address, phone, logo_url))
            school_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return school_id
        except sqlite3.IntegrityError:
            conn.close()
            return None

class User:
    """用户模型类"""
    
    def __init__(self, db):
        self.db = db
    
    def create(self, username, email, password, school_id, is_admin=False):
        """创建新用户"""
        conn = self.db.get_connection()
        password_hash = generate_password_hash(password)
        
        try:
            cursor = conn.execute(
                'INSERT INTO users (username, email, password_hash, school_id, is_admin) VALUES (?, ?, ?, ?, ?)',
                (username, email, password_hash, school_id, is_admin)
            )
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return user_id
        except sqlite3.IntegrityError:
            conn.close()
            return None
    
    def get_by_id(self, user_id):
        """根据ID获取用户"""
        conn = self.db.get_connection()
        user = conn.execute('SELECT * FROM users WHERE id = ?', (user_id,)).fetchone()
        conn.close()
        return user
    
    def get_by_username(self, username):
        """根据用户名获取用户"""
        conn = self.db.get_connection()
        user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()
        conn.close()
        return user
    
    def get_by_email(self, email):
        """根据邮箱获取用户"""
        conn = self.db.get_connection()
        user = conn.execute('SELECT * FROM users WHERE email = ?', (email,)).fetchone()
        conn.close()
        return user
    
    def verify_password(self, user, password):
        """验证密码"""
        return check_password_hash(user['password_hash'], password)
    
    def get_user_with_school(self, user_id):
        """获取用户及其学校信息"""
        conn = self.db.get_connection()
        user = conn.execute('''
            SELECT u.*, s.name as school_name 
            FROM users u 
            JOIN schools s ON u.school_id = s.id 
            WHERE u.id = ?
        ''', (user_id,)).fetchone()
        conn.close()
        return user

class Post:
    """帖子模型类"""
    
    def __init__(self, db):
        self.db = db
    
    def create(self, title, content, user_id, school_id, images=None):
        """创建新帖子"""
        conn = self.db.get_connection()
        images_json = json.dumps(images) if images else None
        
        cursor = conn.execute(
            'INSERT INTO posts (title, content, user_id, school_id, images) VALUES (?, ?, ?, ?, ?)',
            (title, content, user_id, school_id, images_json)
        )
        post_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return post_id
    
    def get_by_id(self, post_id):
        """根据ID获取帖子"""
        conn = self.db.get_connection()
        post = conn.execute('''
            SELECT p.*, u.username, s.name as school_name 
            FROM posts p 
            JOIN users u ON p.user_id = u.id 
            JOIN schools s ON p.school_id = s.id 
            WHERE p.id = ?
        ''', (post_id,)).fetchone()
        conn.close()
        return post
    
    def get_by_school(self, school_id, limit=20, offset=0):
        """获取学校的帖子列表"""
        conn = self.db.get_connection()
        posts = conn.execute('''
            SELECT p.*, u.username 
            FROM posts p 
            JOIN users u ON p.user_id = u.id 
            WHERE p.school_id = ? 
            ORDER BY p.created_at DESC 
            LIMIT ? OFFSET ?
        ''', (school_id, limit, offset)).fetchall()
        conn.close()
        return posts
    
    def get_hot_posts(self, limit=10):
        """获取热门帖子"""
        conn = self.db.get_connection()
        posts = conn.execute('''
            SELECT p.*, u.username, s.name as school_name 
            FROM posts p 
            JOIN users u ON p.user_id = u.id 
            JOIN schools s ON p.school_id = s.id 
            ORDER BY (p.view_count + p.comment_count * 2) DESC 
            LIMIT ?
        ''', (limit,)).fetchall()
        conn.close()
        return posts
    
    def increment_view_count(self, post_id):
        """增加帖子浏览量"""
        conn = self.db.get_connection()
        conn.execute('UPDATE posts SET view_count = view_count + 1 WHERE id = ?', (post_id,))
        conn.commit()
        conn.close()
    
    def search_in_school(self, school_id, keyword, limit=20):
        """在学校板块内搜索帖子"""
        conn = self.db.get_connection()
        posts = conn.execute('''
            SELECT p.*, u.username 
            FROM posts p 
            JOIN users u ON p.user_id = u.id 
            WHERE p.school_id = ? AND (p.title LIKE ? OR p.content LIKE ?) 
            ORDER BY p.created_at DESC 
            LIMIT ?
        ''', (school_id, f'%{keyword}%', f'%{keyword}%', limit)).fetchall()
        conn.close()
        return posts

class Comment:
    """评论模型类"""
    
    def __init__(self, db):
        self.db = db
    
    def create(self, content, post_id, user_id, parent_id=None):
        """创建新评论"""
        conn = self.db.get_connection()
        
        # 插入评论
        cursor = conn.execute(
            'INSERT INTO comments (content, post_id, user_id, parent_id) VALUES (?, ?, ?, ?)',
            (content, post_id, user_id, parent_id)
        )
        comment_id = cursor.lastrowid
        
        # 更新帖子评论数
        conn.execute('UPDATE posts SET comment_count = comment_count + 1 WHERE id = ?', (post_id,))
        
        conn.commit()
        conn.close()
        return comment_id
    
    def get_by_post(self, post_id):
        """获取帖子的所有评论"""
        conn = self.db.get_connection()
        comments = conn.execute('''
            SELECT c.*, u.username 
            FROM comments c 
            JOIN users u ON c.user_id = u.id 
            WHERE c.post_id = ? 
            ORDER BY c.created_at ASC
        ''', (post_id,)).fetchall()
        conn.close()
        return comments