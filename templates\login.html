{% extends "base.html" %}

{% block title %}登录 - 星际论坛{% endblock %}

{% block content %}
<div class="space-container">
    <!-- 螺旋星系背景 -->
    <div class="spiral-galaxy-bg">
        <!-- 主螺旋星系 -->
        <div class="main-spiral">
            <div class="spiral-rings">
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
            </div>
            <div class="spiral-particles" id="spiralParticles"></div>
        </div>
        
        <!-- 第二层螺旋星系 -->
        <div class="secondary-spiral">
            <div id="secondaryParticles"></div>
        </div>
        
        <!-- 浮动星尘 -->
        <div id="floatingStardust"></div>
        
        <!-- 流星效果 -->
        <div id="meteors"></div>
        
        <!-- 径向渐变覆盖层 -->
        <div class="radial-overlay"></div>
    </div>
    
    <div class="space-content flex items-center justify-center min-h-screen">
        <div class="w-full max-w-md mx-4">
            <!-- 返回首页按钮 -->
            <a href="{{ url_for('index') }}" class="inline-flex items-center text-cyan-400 hover:text-cyan-300 transition-colors duration-300 mb-8 group cursor-pointer">
                <i class="fas fa-arrow-left mr-2 group-hover:-translate-x-1 transition-transform duration-300"></i>
                返回首页
            </a>
            
            <!-- 登录卡片 -->
            <div class="modern-card">
                <div class="card-glow-top"></div>
                <div class="card-glow-bottom"></div>
                
                <div class="relative z-10">
                    <!-- 标题 -->
                    <div class="text-center mb-8">
                        <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-500 mb-2 animate-pulse">
                            欢迎回来！
                        </h1>
                        <p class="text-gray-300">请登录您的账户</p>
                    </div>
            
                    <!-- 登录表单 -->
                    <form method="POST" class="space-y-6">
                        <!-- 用户名/邮箱输入框 -->
                        <div class="group">
                            <label for="username" class="block text-sm font-medium text-gray-300 mb-2 group-focus-within:text-cyan-400 transition-colors duration-300">
                                用户名/邮箱
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-user text-gray-400 group-focus-within:text-cyan-400 transition-colors duration-300"></i>
                                </div>
                                <input
                                    type="text"
                                    id="username"
                                    name="username"
                                    class="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400/50 transition-all duration-300 backdrop-blur-sm"
                                    placeholder="请输入用户名或邮箱"
                                    required
                                />
                            </div>
                        </div>

                        <!-- 密码输入框 -->
                        <div class="group">
                            <label for="password" class="block text-sm font-medium text-gray-300 mb-2 group-focus-within:text-cyan-400 transition-colors duration-300">
                                密码
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-gray-400 group-focus-within:text-cyan-400 transition-colors duration-300"></i>
                                </div>
                                <input
                                    type="password"
                                    id="password"
                                    name="password"
                                    class="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400/50 transition-all duration-300 backdrop-blur-sm"
                                    placeholder="请输入密码"
                                    required
                                />
                            </div>
                        </div>

                        <!-- 登录按钮 -->
                        <button
                            type="submit"
                            class="w-full modern-btn modern-btn-primary justify-center"
                        >
                            <span>
                                <i class="fas fa-sign-in-alt"></i> 登录
                            </span>
                        </button>
                    </form>

                    <!-- 注册链接 -->
                    <div class="mt-8 text-center">
                        <p class="text-gray-300">
                            没有账户？
                            <a href="{{ url_for('register') }}" class="text-cyan-400 hover:text-cyan-300 transition-colors duration-300 ml-2 cursor-pointer hover:underline">
                                点击这里注册
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- 粒子效果 -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>

<script>
// 表单验证增强
document.querySelector('form').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    
    if (!username || !password) {
        e.preventDefault();
        alert('请填写完整的登录信息');
        return false;
    }
});

// 回车键快速登录
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const form = document.querySelector('form');
        if (form) {
            form.submit();
        }
    }
});
</script>
{% endblock %}