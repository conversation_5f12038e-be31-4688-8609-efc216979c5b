{% extends "base.html" %}

{% block title %}学校列表 - 大学生论坛{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">全国高校列表</h1>
        
        <!-- 筛选和搜索区域 -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <form method="GET" action="{{ url_for('schools_list') }}" class="space-y-4">
                <!-- 搜索框 -->
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" 
                               name="q" 
                               value="{{ keyword or '' }}" 
                               placeholder="搜索学校名称..." 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <button type="submit" 
                            class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-search mr-2"></i>搜索
                    </button>
                </div>
                
                <!-- 筛选选项 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- 省份筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">省份</label>
                        <select name="province" 
                                id="province-select"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部省份</option>
                            {% for province in provinces %}
                            <option value="{{ province }}" 
                                    {% if selected_province == province %}selected{% endif %}>
                                {{ province }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- 城市筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">城市</label>
                        <select name="city" 
                                id="city-select"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部城市</option>
                            {% for city in cities %}
                            <option value="{{ city }}" 
                                    {% if selected_city == city %}selected{% endif %}>
                                {{ city }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- 学校类型筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">学校类型</label>
                        <select name="type" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部类型</option>
                            {% for school_type in school_types %}
                            <option value="{{ school_type }}" 
                                    {% if selected_type == school_type %}selected{% endif %}>
                                {{ school_type }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <!-- 清除筛选按钮 -->
                <div class="flex justify-between items-center">
                    <a href="{{ url_for('schools_list') }}" 
                       class="text-gray-600 hover:text-gray-800 transition-colors">
                        <i class="fas fa-times mr-1"></i>清除筛选
                    </a>
                    <span class="text-sm text-gray-600">
                        共找到 <strong>{{ schools|length }}</strong> 所学校
                    </span>
                </div>
            </form>
        </div>
        
        <!-- 学校列表 -->
        {% if schools %}
        <div id="schools-grid" class="grid grid-6">
            {% for school in schools %}
            <div class="school-card" onclick="location.href='{{ url_for('school_board', school_id=school.id) }}'">
                <i class="fas fa-university"></i>
                <h3>{{ school.name }}</h3>
                <p>{{ school.province }} {{ school.city }}</p>
                <span class="badge" style="background-color: #e3f2fd; color: #1976d2;">{{ school.school_type }}</span>
                {% if school.level != school.school_type %}
                <span class="badge" style="background-color: #e8f5e8; color: #388e3c;">{{ school.level }}</span>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-12">
            <div class="text-gray-400 mb-4">
                <i class="fas fa-search text-6xl"></i>
            </div>
            <h3 class="text-xl font-medium text-gray-600 mb-2">没有找到匹配的学校</h3>
            <p class="text-gray-500 mb-4">请尝试调整搜索条件或筛选选项</p>
            <a href="{{ url_for('schools_list') }}" 
               class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-refresh mr-2"></i>
                查看全部学校
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
// 省份变化时更新城市列表
document.getElementById('province-select').addEventListener('change', function() {
    const province = this.value;
    const citySelect = document.getElementById('city-select');
    
    // 清空城市选项
    citySelect.innerHTML = '<option value="">全部城市</option>';
    
    if (province) {
        // 获取该省份的城市列表
        fetch(`/api/cities/${encodeURIComponent(province)}`)
            .then(response => response.json())
            .then(data => {
                data.cities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    citySelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('获取城市列表失败:', error);
            });
    }
});
</script>
{% endblock %}