#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高校数据导入脚本
生成全国各省市高校数据，模拟2854所高校的完整数据库
"""

import sqlite3
import random
from config import Config

# 全国省份和主要城市数据
PROVINCE_CITIES = {
    '北京': ['北京市'],
    '天津': ['天津市'],
    '上海': ['上海市'],
    '重庆': ['重庆市'],
    '河北': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市'],
    '山西': ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市', '忻州市', '临汾市', '吕梁市'],
    '内蒙古': ['呼和浩特市', '包头市', '乌海市', '赤峰市', '通辽市', '鄂尔多斯市', '呼伦贝尔市', '巴彦淖尔市', '乌兰察布市'],
    '辽宁': ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市', '阜新市', '辽阳市', '盘锦市', '铁岭市', '朝阳市', '葫芦岛市'],
    '吉林': ['长春市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市', '白城市'],
    '黑龙江': ['哈尔滨市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市', '七台河市', '牡丹江市', '黑河市', '绥化市'],
    '江苏': ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'],
    '浙江': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市'],
    '安徽': ['合肥市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市', '安庆市', '黄山市', '滁州市', '阜阳市', '宿州市', '六安市', '亳州市', '池州市', '宣城市'],
    '福建': ['福州市', '厦门市', '莆田市', '三明市', '泉州市', '漳州市', '南平市', '龙岩市', '宁德市'],
    '江西': ['南昌市', '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市', '宜春市', '抚州市', '上饶市'],
    '山东': ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市', '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市'],
    '河南': ['郑州市', '开封市', '洛阳市', '平顶山市', '安阳市', '鹤壁市', '新乡市', '焦作市', '濮阳市', '许昌市', '漯河市', '三门峡市', '南阳市', '商丘市', '信阳市', '周口市', '驻马店市'],
    '湖北': ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市', '荆州市', '黄冈市', '咸宁市', '随州市'],
    '湖南': ['长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市', '益阳市', '郴州市', '永州市', '怀化市', '娄底市'],
    '广东': ['广州市', '韶关市', '深圳市', '珠海市', '汕头市', '佛山市', '江门市', '湛江市', '茂名市', '肇庆市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],
    '广西': ['南宁市', '柳州市', '桂林市', '梧州市', '北海市', '防城港市', '钦州市', '贵港市', '玉林市', '百色市', '贺州市', '河池市', '来宾市', '崇左市'],
    '海南': ['海口市', '三亚市', '三沙市', '儋州市'],
    '四川': ['成都市', '自贡市', '攀枝花市', '泸州市', '德阳市', '绵阳市', '广元市', '遂宁市', '内江市', '乐山市', '南充市', '眉山市', '宜宾市', '广安市', '达州市', '雅安市', '巴中市', '资阳市'],
    '贵州': ['贵阳市', '六盘水市', '遵义市', '安顺市', '毕节市', '铜仁市'],
    '云南': ['昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市'],
    '西藏': ['拉萨市', '日喀则市', '昌都市', '林芝市', '山南市', '那曲市'],
    '陕西': ['西安市', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市', '安康市', '商洛市'],
    '甘肃': ['兰州市', '嘉峪关市', '金昌市', '白银市', '天水市', '武威市', '张掖市', '平凉市', '酒泉市', '庆阳市', '定西市', '陇南市'],
    '青海': ['西宁市', '海东市'],
    '宁夏': ['银川市', '石嘴山市', '吴忠市', '固原市', '中卫市'],
    '新疆': ['乌鲁木齐市', '克拉玛依市', '吐鲁番市', '哈密市', '昌吉市', '博尔塔拉市', '巴音郭楞市', '阿克苏市', '克孜勒苏市', '喀什市', '和田市', '伊犁市', '塔城市', '阿勒泰市']
}

# 学校类型和层次
SCHOOL_TYPES = ['985', '211', '双一流', '普通本科', '民办本科', '独立学院', '高职专科', '民办专科']
SCHOOL_LEVELS = ['本科', '专科', '研究生院']

# 学校名称后缀
UNIVERSITY_SUFFIXES = [
    '大学', '学院', '科技大学', '理工大学', '师范大学', '医科大学', '农业大学', '工业大学',
    '交通大学', '财经大学', '政法大学', '外国语大学', '艺术学院', '体育学院', '音乐学院',
    '美术学院', '医学院', '职业技术学院', '职业学院', '技术学院'
]

# 学校名称前缀（地名相关）
UNIVERSITY_PREFIXES = [
    '', '中国', '华北', '华东', '华南', '华中', '华西', '东北', '西北', '西南', '中南',
    '第一', '第二', '第三', '中央', '国际', '现代', '新', '老'
]

def generate_school_data():
    """生成学校数据"""
    schools = []
    used_names = set()  # 使用集合来快速检查重复名称
    school_id = 1
    
    for province, cities in PROVINCE_CITIES.items():
        # 每个省份生成不同数量的学校
        if province in ['北京', '上海', '江苏', '广东', '山东']:
            schools_per_province = random.randint(60, 80)  # 减少数量避免重复
        elif province in ['天津', '重庆', '浙江', '湖北', '湖南', '四川']:
            schools_per_province = random.randint(40, 60)
        elif province in ['河北', '山西', '辽宁', '吉林', '黑龙江', '安徽', '福建', '江西', '河南', '陕西']:
            schools_per_province = random.randint(30, 50)
        else:
            schools_per_province = random.randint(15, 35)  # 其他省份
        
        for i in range(schools_per_province):
            city = random.choice(cities)
            
            # 生成学校名称，增加更多变化
            attempts = 0
            while attempts < 50:  # 最多尝试50次
                if random.random() < 0.4:  # 40%概率使用地名
                    if random.random() < 0.5:
                        name_base = province.replace('省', '').replace('市', '').replace('自治区', '')
                    else:
                        name_base = city.replace('市', '')
                else:  # 60%概率使用其他前缀
                    prefix = random.choice(UNIVERSITY_PREFIXES)
                    if prefix:
                        name_base = prefix + city.replace('市', '')
                    else:
                        name_base = city.replace('市', '')
                
                suffix = random.choice(UNIVERSITY_SUFFIXES)
                
                # 添加更多变化来避免重复
                if random.random() < 0.2:  # 20%概率添加数字
                    number = random.choice(['第一', '第二', '第三', '一', '二', '三'])
                    school_name = f"{name_base}{number}{suffix}"
                elif random.random() < 0.3:  # 30%概率添加特色词
                    specialty = random.choice(['工程', '科技', '现代', '新', '国际', '民族', '石油', '电力', '铁道', '航空'])
                    school_name = f"{name_base}{specialty}{suffix}"
                else:
                    school_name = f"{name_base}{suffix}"
                
                # 检查名称是否重复
                if school_name not in used_names:
                    used_names.add(school_name)
                    break
                
                attempts += 1
            
            # 如果仍然重复，添加序号
            if school_name in used_names:
                counter = 1
                original_name = school_name
                while f"{original_name}({counter})" in used_names:
                    counter += 1
                school_name = f"{original_name}({counter})"
                used_names.add(school_name)
            
            # 确定学校类型和层次
            school_type = random.choices(
                SCHOOL_TYPES,
                weights=[2, 8, 15, 35, 15, 10, 12, 3],  # 权重分配
                k=1
            )[0]
            
            if school_type in ['985', '211', '双一流']:
                level = '本科'
            elif school_type in ['普通本科', '民办本科', '独立学院']:
                level = random.choice(['本科', '研究生院']) if random.random() < 0.3 else '本科'
            else:
                level = '专科'
            
            # 生成其他信息
            website = f"https://www.{school_name.lower().replace('大学', 'edu').replace('学院', 'college')}.edu.cn"
            phone = f"0{random.randint(10, 99)}-{random.randint(10000000, 99999999)}"
            
            # 生成地址
            district_names = ['新城区', '老城区', '开发区', '高新区', '经济区', '大学城', '科技园区']
            street_names = ['学府路', '大学路', '教育路', '文化路', '知识路', '学院路', '书院街', '文明街']
            address = f"{province}{city}{random.choice(district_names)}{random.choice(street_names)}{random.randint(1, 999)}号"
            
            school_data = (
                school_name,
                f"{school_name}官方论坛板块",
                province,
                city,
                school_type,
                level,
                website,
                address,
                phone,
                ''  # logo_url 暂时为空
            )
            
            schools.append(school_data)
            school_id += 1
    
    return schools

def import_schools_to_database(schools_data):
    """将学校数据导入数据库"""
    conn = sqlite3.connect(Config.DATABASE)
    
    try:
        # 清除现有的非核心学校数据（保留前4所核心学校）
        conn.execute('DELETE FROM schools WHERE id > 4')
        
        # 批量插入新数据
        conn.executemany('''
            INSERT INTO schools (name, description, province, city, school_type, level, website, address, phone, logo_url) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', schools_data)
        
        conn.commit()
        
        # 获取统计信息
        total_count = conn.execute('SELECT COUNT(*) FROM schools').fetchone()[0]
        province_count = conn.execute('SELECT COUNT(DISTINCT province) FROM schools WHERE province IS NOT NULL').fetchone()[0]
        type_stats = conn.execute('''
            SELECT school_type, COUNT(*) as count 
            FROM schools 
            WHERE school_type IS NOT NULL 
            GROUP BY school_type 
            ORDER BY count DESC
        ''').fetchall()
        
        print(f"\n=== 数据导入完成 ===")
        print(f"总学校数量: {total_count}")
        print(f"覆盖省份数量: {province_count}")
        print(f"\n学校类型分布:")
        for school_type, count in type_stats:
            print(f"  {school_type}: {count}所")
        
        return True
        
    except Exception as e:
        print(f"导入数据时出现错误: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def main():
    """主函数"""
    print("开始生成全国高校数据...")
    
    # 生成学校数据
    schools_data = generate_school_data()
    print(f"生成了 {len(schools_data)} 所学校的数据")
    
    # 导入到数据库
    print("\n开始导入数据库...")
    success = import_schools_to_database(schools_data)
    
    if success:
        print("\n🎉 全国高校数据导入成功！")
        print("论坛现在支持全国各省市的高校用户注册和讨论！")
    else:
        print("\n❌ 数据导入失败，请检查错误信息")

if __name__ == '__main__':
    main()