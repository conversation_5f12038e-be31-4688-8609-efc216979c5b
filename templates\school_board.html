{% extends "base.html" %}

{% block title %}{{ school.name }} - 大学生论坛{% endblock %}

{% block content %}
<!-- 学校板块头部 -->
<div class="school-header">
    <div class="card">
        <div class="card-body">
            <div class="flex" style="justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 20px;">
                <div>
                    <h1 style="color: #2196F3; margin-bottom: 5px;">
                        <i class="fas fa-university"></i> {{ school.name }}
                    </h1>
                    <p style="color: #666; margin: 0;">{{ school.description or '欢迎来到' + school.name + '论坛板块' }}</p>
                </div>
                <div class="flex gap-10">
                    {% if current_user.is_authenticated and current_user.school_id == school.id %}
                    <a href="{{ url_for('create_post') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 发新帖
                    </a>
                    {% endif %}
                    <a href="{{ url_for('index') }}" class="btn btn-outline">
                        <i class="fas fa-home"></i> 返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索栏 -->
<div class="search-section">
    <div class="card">
        <div class="card-body">
            <form method="GET" action="{{ url_for('search_posts', school_id=school.id) }}" class="flex gap-10">
                <input type="text" name="q" class="form-control" placeholder="在{{ school.name }}板块内搜索帖子..." 
                       value="{{ request.args.get('q', '') }}" style="flex: 1;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </form>
        </div>
    </div>
</div>

<!-- 帖子列表 -->
<div class="posts-section">
    {% if posts %}
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-list"></i> 最新帖子
                </h2>
                <p style="color: #666; margin: 0;">{{ school.name }}同学们的最新讨论</p>
            </div>
            <div class="card-body" style="padding: 0;">
                {% for post in posts %}
                <div class="post-item" style="margin: 0; border-radius: 0; box-shadow: none; border-bottom: 1px solid #eee;">
                    <a href="{{ url_for('post_detail', post_id=post.id) }}" class="post-title">
                        {{ post.title }}
                    </a>
                    <div class="post-meta">
                        <span><i class="fas fa-user"></i> {{ post.username }}</span>
                        <span><i class="fas fa-clock"></i> {{ post.created_at }}</span>
                    </div>
                    <div class="post-content">
                        {{ post.content[:200] }}{% if post.content|length > 200 %}...{% endif %}
                    </div>
                    <div class="post-stats">
                        <span><i class="fas fa-eye"></i> {{ post.view_count }} 浏览</span>
                        <span><i class="fas fa-comment"></i> {{ post.comment_count }} 评论</span>
                        <span><i class="fas fa-calendar"></i> {{ post.created_at.split(' ')[0] if ' ' in post.created_at else post.created_at }}</span>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- 分页导航 -->
        {% if posts|length == 20 %}
        <div class="pagination-section text-center" style="margin-top: 20px;">
            <div class="flex-center gap-10">
                {% if page > 1 %}
                <a href="{{ url_for('school_board', school_id=school.id, page=page-1) }}" class="btn btn-outline">
                    <i class="fas fa-chevron-left"></i> 上一页
                </a>
                {% endif %}
                <span class="btn" style="background: #f5f5f5; cursor: default;">第 {{ page }} 页</span>
                <a href="{{ url_for('school_board', school_id=school.id, page=page+1) }}" class="btn btn-outline">
                    下一页 <i class="fas fa-chevron-right"></i>
                </a>
            </div>
        </div>
        {% endif %}
    {% else %}
        <!-- 空状态 -->
        <div class="card">
            <div class="card-body text-center" style="padding: 60px 20px;">
                <i class="fas fa-comments" style="font-size: 64px; color: #ddd; margin-bottom: 20px;"></i>
                <h3 style="color: #666; margin-bottom: 15px;">还没有帖子</h3>
                <p style="color: #999; margin-bottom: 25px;">{{ school.name }}板块还没有同学发帖，快来发布第一个帖子吧！</p>
                {% if current_user.is_authenticated and current_user.school_id == school.id %}
                <a href="{{ url_for('create_post') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 发布第一个帖子
                </a>
                {% else %}
                <div class="flex-center gap-10">
                    <a href="{{ url_for('login') }}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> 登录后发帖
                    </a>
                    <a href="{{ url_for('register') }}" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> 注册账号
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>

<!-- 发帖提示 -->
{% if current_user.is_authenticated and current_user.school_id != school.id %}
<div class="card" style="background: #fff3cd; border-left: 4px solid #ffc107;">
    <div class="card-body">
        <h4 style="color: #856404; margin-bottom: 10px;">
            <i class="fas fa-info-circle"></i> 提示
        </h4>
        <p style="color: #856404; margin: 0;">你只能在自己学校的论坛板块发帖。如需发帖，请前往你所在学校的板块。</p>
    </div>
</div>
{% endif %}

<!-- 浮动发帖按钮 (移动端) -->
{% if current_user.is_authenticated and current_user.school_id == school.id %}
<div class="floating-btn" style="position: fixed; bottom: 20px; right: 20px; z-index: 100;">
    <a href="{{ url_for('create_post') }}" class="btn btn-primary" 
       style="border-radius: 50%; width: 56px; height: 56px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);">
        <i class="fas fa-plus" style="font-size: 20px;"></i>
    </a>
</div>
{% endif %}

<style>
@media (min-width: 769px) {
    .floating-btn {
        display: none;
    }
}

@media (max-width: 768px) {
    .school-header .flex {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .search-section .flex {
        flex-direction: column;
    }
    
    .search-section .form-control {
        margin-bottom: 10px;
    }
}
</style>
{% endblock %}