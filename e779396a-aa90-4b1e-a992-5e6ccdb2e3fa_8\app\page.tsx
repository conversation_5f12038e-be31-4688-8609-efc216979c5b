'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function Home() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // 生成固定的螺旋粒子数据，避免水合错误
  const generateSpiralParticles = (count: number) => {
    const particles = [];
    for (let i = 0; i < count; i++) {
      const angle = (i * 15) % 360;
      const radius = 50 + (i * 3);
      const x = Math.cos((angle * Math.PI) / 180) * radius;
      const y = Math.sin((angle * Math.PI) / 180) * radius;
      
      particles.push({
        id: i,
        x,
        y,
        opacity: 0.2 + ((i % 3) * 0.2),
        color: ['#00ffff', '#ff00ff', '#ffff00', '#ff6b6b'][i % 4],
        delay: i * 0.1,
        glowSize: 2 + ((i % 3) + 1)
      });
    }
    return particles;
  };

  const generateSecondaryParticles = (count: number) => {
    const particles = [];
    for (let i = 0; i < count; i++) {
      const angle = (i * 22.5) % 360;
      const radius = 100 + (i * 5);
      const x = Math.cos((angle * Math.PI) / 180) * radius;
      const y = Math.sin((angle * Math.PI) / 180) * radius;
      
      particles.push({
        id: i,
        x,
        y,
        opacity: 0.1 + ((i % 4) * 0.15),
        delay: i * 0.15
      });
    }
    return particles;
  };

  const generateFloatingParticles = (count: number) => {
    const particles = [];
    for (let i = 0; i < count; i++) {
      particles.push({
        id: i,
        left: (i * 7) % 100,
        top: (i * 11) % 100,
        opacity: 0.3 + ((i % 3) * 0.2),
        delay: (i % 10) * 0.5,
        duration: 2 + ((i % 3) + 1)
      });
    }
    return particles;
  };

  const generateMeteors = (count: number) => {
    const meteors = [];
    for (let i = 0; i < count; i++) {
      meteors.push({
        id: i,
        left: (i * 13) % 100,
        top: (i * 17) % 100,
        duration: 3 + ((i % 3) + 1),
        delay: (i % 8) * 0.6
      });
    }
    return meteors;
  };

  const spiralParticles = generateSpiralParticles(120);
  const secondaryParticles = generateSecondaryParticles(80);
  const floatingParticles = generateFloatingParticles(200);
  const meteors = generateMeteors(8);

  if (!isClient) {
    return (
      <div className="relative min-h-screen overflow-hidden bg-black flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 font-['Pacifico']">
            星际论坛
          </h1>
          <p className="text-xl text-gray-300 mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen overflow-hidden bg-black">
      {/* 动态螺旋星系背景 */}
      <div className="absolute inset-0">
        {/* 主螺旋星系 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="relative w-[800px] h-[800px] animate-spin" style={{ animationDuration: '60s' }}>
            {/* 螺旋臂 */}
            <div className="absolute inset-0 opacity-80">
              <div className="absolute w-full h-full rounded-full border-4 border-blue-400/30 animate-pulse"></div>
              <div className="absolute w-5/6 h-5/6 top-[8.33%] left-[8.33%] rounded-full border-3 border-purple-400/40 animate-pulse" style={{ animationDelay: '1s' }}></div>
              <div className="absolute w-4/6 h-4/6 top-1/6 left-1/6 rounded-full border-2 border-cyan-400/50 animate-pulse" style={{ animationDelay: '2s' }}></div>
              <div className="absolute w-3/6 h-3/6 top-1/4 left-1/4 rounded-full border-2 border-pink-400/60 animate-pulse" style={{ animationDelay: '3s' }}></div>
            </div>
            
            {/* 螺旋粒子点 */}
            {spiralParticles.map((particle) => (
              <div
                key={particle.id}
                className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
                style={{
                  left: `calc(50% + ${particle.x}px)`,
                  top: `calc(50% + ${particle.y}px)`,
                  animationDelay: `${particle.delay}s`,
                  opacity: particle.opacity,
                  boxShadow: `0 0 ${particle.glowSize}px ${particle.color}`
                }}
              />
            ))}
          </div>
        </div>

        {/* 第二层螺旋星系 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="relative w-[1200px] h-[1200px] animate-spin" style={{ animationDuration: '-90s' }}>
            {secondaryParticles.map((particle) => (
              <div
                key={particle.id}
                className="absolute w-0.5 h-0.5 bg-gradient-to-r from-blue-400 to-purple-600 rounded-full animate-pulse"
                style={{
                  left: `calc(50% + ${particle.x}px)`,
                  top: `calc(50% + ${particle.y}px)`,
                  animationDelay: `${particle.delay}s`,
                  opacity: particle.opacity,
                  boxShadow: `0 0 2px #4F46E5`
                }}
              />
            ))}
          </div>
        </div>

        {/* 浮动星尘粒子 */}
        {floatingParticles.map((particle) => (
          <div
            key={particle.id}
            className="absolute w-px h-px bg-white rounded-full animate-pulse"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              animationDelay: `${particle.delay}s`,
              animationDuration: `${particle.duration}s`,
              opacity: particle.opacity,
              transform: `translate(${(mousePosition.x - 50) * 0.1}px, ${(mousePosition.y - 50) * 0.1}px)`
            }}
          />
        ))}

        {/* 流星效果 */}
        {meteors.map((meteor) => (
          <div
            key={meteor.id}
            className="absolute w-1 h-1 bg-gradient-to-r from-white to-transparent rounded-full"
            style={{
              left: `${meteor.left}%`,
              top: `${meteor.top}%`,
              animation: `meteor ${meteor.duration}s linear infinite`,
              animationDelay: `${meteor.delay}s`,
              transform: 'rotate(-45deg)',
              boxShadow: '0 0 20px 2px rgba(255,255,255,0.8), 0 0 40px 4px rgba(100,200,255,0.3)',
            }}
          />
        ))}

        {/* 径向渐变覆盖层 */}
        <div 
          className="absolute inset-0 opacity-60"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, 
              rgba(0,0,0,0) 0%, 
              rgba(30,20,60,0.3) 40%, 
              rgba(0,0,0,0.8) 100%)`
          }}
        />
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen text-center px-8">
        {/* 主标题 */}
        <div className="mb-8">
          <h1 className="text-6xl md:text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 animate-pulse mb-4 font-['Pacifico']">
            星际论坛
          </h1>
          <div className="h-1 w-40 mx-auto bg-gradient-to-r from-transparent via-white to-transparent animate-pulse"></div>
        </div>

        {/* 副标题 */}
        <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl leading-relaxed">
          探索无限宇宙，连接每一颗星球上的智慧生命
          <br />
          <span className="text-cyan-400 animate-pulse">在这里，每个想法都能照亮黑暗的太空</span>
        </p>

        {/* 行动按钮 */}
        <div className="space-y-6 md:space-y-0 md:space-x-8 md:flex">
          <Link href="/login">
            <button className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full text-white font-semibold text-lg overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/50 whitespace-nowrap cursor-pointer">
              <span className="relative z-10 flex items-center justify-center">
                <i className="ri-rocket-2-line mr-2 text-xl w-6 h-6 flex items-center justify-center"></i>
                开始探索
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
            </button>
          </Link>
          
          <Link href="/login">
            <button className="group relative px-8 py-4 border-2 border-cyan-400 rounded-full text-cyan-400 font-semibold text-lg overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-cyan-400/50 whitespace-nowrap cursor-pointer">
              <span className="relative z-10 flex items-center justify-center">
                <i className="ri-planet-line mr-2 text-xl w-6 h-6 flex items-center justify-center"></i>
                发现星球
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-gray-800 to-gray-900 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <span className="absolute inset-0 flex items-center justify-center text-cyan-300 font-semibold opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <i className="ri-planet-line mr-2 text-xl w-6 h-6 flex items-center justify-center"></i>
                发现星球
              </span>
            </button>
          </Link>
        </div>

        {/* 统计信息 */}
        <div className="mt-16 grid grid-cols-3 gap-8 text-center">
          <div className="group hover:scale-110 transition-transform duration-300 cursor-pointer">
            <div className="text-3xl md:text-4xl font-bold text-cyan-400 mb-2 animate-pulse">
              2,847
            </div>
            <div className="text-gray-400 group-hover:text-white transition-colors duration-300">活跃星球</div>
          </div>
          <div className="group hover:scale-110 transition-transform duration-300 cursor-pointer">
            <div className="text-3xl md:text-4xl font-bold text-purple-400 mb-2 animate-pulse" style={{ animationDelay: '1s' }}>
              156K
            </div>
            <div className="text-gray-400 group-hover:text-white transition-colors duration-300">星际居民</div>
          </div>
          <div className="group hover:scale-110 transition-transform duration-300 cursor-pointer">
            <div className="text-3xl md:text-4xl font-bold text-pink-400 mb-2 animate-pulse" style={{ animationDelay: '2s' }}>
              98.7M
            </div>
            <div className="text-gray-400 group-hover:text-white transition-colors duration-300">星际对话</div>
          </div>
        </div>
      </div>

      {/* CSS 动画定义 */}
      <style jsx>{`
        @keyframes meteor {
          0% {
            transform: rotate(-45deg) translateX(-100px);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: rotate(-45deg) translateX(calc(100vw + 100px));
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
}