#!/usr/bin/env python3
import requests
import json

def test_api_endpoints():
    base_url = "http://127.0.0.1:5000"
    
    print("=== 测试API接口功能 ===")
    
    # 测试省份列表API
    try:
        response = requests.get(f"{base_url}/api/provinces")
        if response.status_code == 200:
            provinces = response.json()
            print(f"✓ 省份列表API正常，共{len(provinces)}个省份")
            print(f"  前5个省份: {provinces[:5]}")
        else:
            print(f"✗ 省份列表API错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 省份列表API异常: {e}")
    
    # 测试学校类型API
    try:
        response = requests.get(f"{base_url}/api/school_types")
        if response.status_code == 200:
            school_types = response.json()
            print(f"✓ 学校类型API正常，共{len(school_types)}种类型")
            print(f"  学校类型: {school_types}")
        else:
            print(f"✗ 学校类型API错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 学校类型API异常: {e}")
    
    # 测试城市列表API（以北京为例）
    try:
        response = requests.get(f"{base_url}/api/cities/北京")
        if response.status_code == 200:
            cities = response.json()
            print(f"✓ 城市列表API正常，北京有{len(cities)}个城市")
            print(f"  北京城市: {cities}")
        else:
            print(f"✗ 城市列表API错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 城市列表API异常: {e}")
    
    # 测试学校搜索API
    try:
        response = requests.get(f"{base_url}/api/schools/search?keyword=北京大学")
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 学校搜索API正常，找到{len(result['schools'])}所学校")
            if result['schools']:
                school = result['schools'][0]
                print(f"  示例学校: {school['name']} - {school['province']}{school['city']} - {school['school_type']}")
        else:
            print(f"✗ 学校搜索API错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 学校搜索API异常: {e}")
    
    # 测试学校列表页面
    try:
        response = requests.get(f"{base_url}/schools")
        if response.status_code == 200:
            print(f"✓ 学校列表页面正常访问")
            if "学校列表" in response.text:
                print(f"  页面包含学校列表内容")
        else:
            print(f"✗ 学校列表页面错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 学校列表页面异常: {e}")
    
    print("\n=== API测试完成 ===")

if __name__ == '__main__':
    test_api_endpoints()