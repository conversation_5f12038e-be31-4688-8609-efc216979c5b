<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}星际论坛{% endblock %}</title>
    <link href="{{ url_for('static', filename='css/spiral-galaxy-theme.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="{{ url_for('index') }}">
                    <i class="fas fa-rocket"></i>
                    星际论坛
                </a>
            </div>
            
            <div class="nav-menu">
                {% if current_user.is_authenticated %}
                    <div class="nav-user">
                        <span class="welcome-text">欢迎，{{ current_user.username }}</span>
                        <a href="{{ url_for('profile') }}" class="nav-link">
                            <i class="fas fa-user-astronaut"></i> 空间站
                        </a>
                        <a href="{{ url_for('create_post') }}" class="nav-link">
                            <i class="fas fa-satellite"></i> 发射信号
                        </a>
                        <a href="{{ url_for('logout') }}" class="nav-link">
                            <i class="fas fa-space-shuttle"></i> 返回地球
                        </a>
                    </div>
                {% else %}
                    <div class="nav-auth">
                        <a href="{{ url_for('login') }}" class="nav-link">
                            <i class="fas fa-rocket"></i> 登录
                        </a>
                        <a href="{{ url_for('register') }}" class="nav-link register-btn">
                            <i class="fas fa-user-astronaut"></i> 加入星际
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- 消息提示 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message flash-{{ category }}">
                        <span>{{ message }}</span>
                        <button class="flash-close" onclick="this.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- 主要内容 -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="footer-container">
            <p>&copy; 2024 星际论坛. 连接宇宙中每一颗智慧星球.</p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/spiral-galaxy.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>