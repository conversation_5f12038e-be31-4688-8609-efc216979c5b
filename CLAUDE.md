# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 开发命令

### 启动应用
```bash
python app.py
```
应用将在 `http://127.0.0.1:5000` 运行

### 数据库初始化和管理
```bash
# 初始化数据库表结构（app.py 启动时自动执行）
python -c "from models import Database; db = Database(); db.init_database()"

# 导入全国高校数据（生成约2000+所学校）
python import_schools_data.py

# 迁移和更新学校数据库结构
python migrate_schools.py

# 检查数据完整性
python check_data.py
```

### 测试
```bash
# API功能测试（需要应用运行中）
python test_api.py
```

### Vercel部署
```bash
# 部署到Vercel（使用vercel.json配置）
vercel --prod
```

## 代码架构

### 应用结构
- **Flask MVC架构**：主应用入口为 `app.py`，包含所有路由和视图逻辑
- **数据层**：`models.py` 包含所有数据库模型类（Database, School, User, Post, Comment）
- **配置管理**：`config.py` 提供开发和生产环境配置
- **模板系统**：`templates/` 目录包含所有Jinja2模板文件
- **静态资源**：`static/` 目录包含CSS和JavaScript文件

### 核心功能模块

1. **用户系统**
   - 用户注册/登录（支持用户名或邮箱登录）
   - Flask-Login会话管理
   - 用户与学校关联

2. **学校系统**
   - 全国高校数据库（2854所高校）
   - 基于地理位置的学校推荐
   - 省份/城市/类型多维度筛选
   - 学校搜索API

3. **论坛系统**
   - 学校板块帖子发布
   - 帖子浏览和评论
   - 热门帖子算法（浏览量+评论数权重）
   - 评论支持回复嵌套

4. **地理位置功能**
   - IP地址地理定位（使用ip-api.com）
   - 基于位置的本地学校推荐
   - 本地IP地址处理

### 数据库设计

SQLite数据库包含以下核心表：
- `schools`：学校信息（含省份、城市、类型、层次等）
- `users`：用户信息（关联学校ID）
- `posts`：帖子（关联用户和学校）
- `comments`：评论（支持嵌套回复）

### API接口

RESTful API设计：
- `/api/provinces` - 获取省份列表
- `/api/cities/<province>` - 获取指定省份的城市
- `/api/school_types` - 获取学校类型列表
- `/api/schools/search` - 学校搜索（支持关键词、地区、类型筛选）
- `/api/comment` - 添加评论

### 关键配置
- 数据库文件：`forum.db`（SQLite）
- 会话密钥：通过环境变量 `SECRET_KEY` 配置
- 上传文件限制：16MB
- 默认运行端口：5000

### 数据文件
- `aadcc/` 目录包含全国高校原始数据
- `schools_data.rar` 为高校数据压缩包
- `districts.sql` 包含地区数据表

## 开发注意事项

### 数据库操作
- 所有数据库操作使用SQLite row_factory确保返回字典格式
- 数据库连接在每次操作后都会关闭
- 支持事务回滚错误处理

### 安全考虑
- 密码使用Werkzeug安全哈希
- SQL查询使用参数化防止注入
- Flask-Login提供会话安全

### 性能优化
- 数据库索引覆盖主要查询字段
- 分页支持减少单次查询数据量
- IP地理位置查询包含超时和错误处理

### 扩展功能
代码已预留云存储配置（七牛云）供后续扩展使用