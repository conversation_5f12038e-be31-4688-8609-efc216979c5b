/* 星际论坛主题样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #e1e6fa;
    background-color: #000;
    min-height: 100vh;
    overflow-x: hidden;
}

/* 星空背景 */
.stars-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.stars, .stars2, .stars3 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
}

.stars {
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: starsAnimation 150s linear infinite;
}

.stars2 {
    background-image: 
        radial-gradient(1px 1px at 40px 60px, #c0c0ff, transparent),
        radial-gradient(1px 1px at 80px 10px, rgba(192,192,255,0.8), transparent),
        radial-gradient(1px 1px at 120px 50px, #9090ff, transparent);
    background-repeat: repeat;
    background-size: 150px 80px;
    animation: starsAnimation 100s linear infinite;
}

.stars3 {
    background-image: 
        radial-gradient(1px 1px at 60px 20px, #9090ff, transparent),
        radial-gradient(1px 1px at 100px 60px, rgba(144,144,255,0.6), transparent);
    background-repeat: repeat;
    background-size: 120px 60px;
    animation: starsAnimation 50s linear infinite;
}

@keyframes starsAnimation {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(1000px);
    }
}

/* 主容器 */
.space-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
}

.space-content {
    width: 100%;
    max-width: 1200px;
    z-index: 1;
}

/* 星球容器 */
.planet-container {
    position: relative;
    width: 100%;
    height: 100vh;
    min-height: 600px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
}

/* 中央星球 */
.central-planet {
    position: relative;
    width: min(450px, 70vw);
    height: min(450px, 70vw);
    max-width: 500px;
    max-height: 500px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.planet-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(33, 150, 243, 0.3) 0%, rgba(156, 39, 176, 0.3) 50%, rgba(0, 0, 0, 0) 70%);
    filter: blur(20px);
    animation: glowPulse 4s ease-in-out infinite alternate;
}

@keyframes glowPulse {
    0% {
        opacity: 0.7;
        transform: scale(1);
    }
    100% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.planet-surface {
    position: relative;
    width: 90%;
    height: 90%;
    border-radius: 50%;
    background: linear-gradient(135deg, #1a2a3a 0%, #0d1321 100%);
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.8), inset 0 0 50px rgba(0, 0, 0, 0.5);
    overflow: visible;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px;
    text-align: center;
    z-index: 10;
}

/* 轨道和卫星 */
.orbit {
    position: absolute;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.orbit-1 {
    width: min(550px, 80vw);
    height: min(550px, 80vw);
    animation: orbitRotate 20s linear infinite;
}

.orbit-2 {
    width: min(700px, 95vw);
    height: min(700px, 95vw);
    animation: orbitRotate 30s linear infinite reverse;
}

.orbit-3 {
    width: min(850px, 110vw);
    height: min(850px, 110vw);
    animation: orbitRotate 40s linear infinite;
}

@keyframes orbitRotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

.satellite {
    position: absolute;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(100, 100, 255, 0.5) 100%);
    border-radius: 50%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 10px rgba(100, 100, 255, 0.8);
}

/* 文本样式 */
.forum-title {
    margin-bottom: 30px;
    position: relative;
    z-index: 15;
}

.space-title {
    font-size: clamp(2.5rem, 8vw, 4rem);
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(to right, #00c6ff, #92effd, #a16bfe, #ff00aa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 10px rgba(0, 198, 255, 0.5);
    position: relative;
    z-index: 20;
}

.space-subtitle {
    font-size: clamp(1rem, 3vw, 1.2rem);
    color: #ffffff;
    margin-bottom: 10px;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.4;
}

.space-description {
    font-size: clamp(0.9rem, 2.5vw, 1rem);
    color: #a0a8c0;
    max-width: 85%;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.4;
}

/* 按钮样式 */
.space-actions {
    display: flex;
    gap: 20px;
    margin: 30px 0;
    justify-content: center;
    position: relative;
    z-index: 15;
}

.space-btn {
    padding: 12px 24px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.space-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: -1;
}

.space-btn:hover::before {
    transform: translateX(0);
}

.space-btn.primary {
    background: linear-gradient(45deg, #4a4eff, #9000ff);
    color: white;
    box-shadow: 0 4px 15px rgba(74, 78, 255, 0.4);
}

.space-btn.primary:hover {
    box-shadow: 0 6px 20px rgba(74, 78, 255, 0.6);
    transform: translateY(-2px);
}

.space-btn.secondary {
    background: transparent;
    color: #4a4eff;
    border: 2px solid #4a4eff;
    box-shadow: 0 4px 15px rgba(74, 78, 255, 0.2);
}

.space-btn.secondary:hover {
    background: rgba(74, 78, 255, 0.1);
    box-shadow: 0 6px 20px rgba(74, 78, 255, 0.3);
    transform: translateY(-2px);
}

/* 统计数据 */
.space-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 30px;
    position: relative;
    z-index: 15;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00c6ff, #a16bfe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    font-size: 0.9rem;
    color: #a0a8c0;
    margin-top: 5px;
}

/* 粒子效果 */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 0 10px 2px rgba(255, 255, 255, 0.8);
    animation: particleFloat 15s linear infinite;
    opacity: 0;
}

.particle:nth-child(1) {
    top: 10%;
    left: 20%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    top: 40%;
    left: 80%;
    animation-delay: 2s;
}

.particle:nth-child(3) {
    top: 70%;
    left: 10%;
    animation-delay: 4s;
}

.particle:nth-child(4) {
    top: 30%;
    left: 60%;
    animation-delay: 6s;
}

.particle:nth-child(5) {
    top: 80%;
    left: 30%;
    animation-delay: 8s;
}

.particle:nth-child(6) {
    top: 20%;
    left: 50%;
    animation-delay: 10s;
}

.particle:nth-child(7) {
    top: 60%;
    left: 70%;
    animation-delay: 12s;
}

.particle:nth-child(8) {
    top: 90%;
    left: 40%;
    animation-delay: 14s;
}

.particle:nth-child(9) {
    top: 50%;
    left: 90%;
    animation-delay: 16s;
}

.particle:nth-child(10) {
    top: 15%;
    left: 35%;
    animation-delay: 18s;
}

@keyframes particleFloat {
    0% {
        transform: translateY(0) scale(0);
        opacity: 0;
    }
    10% {
        transform: translateY(-20px) scale(1);
        opacity: 1;
    }
    90% {
        transform: translateY(-200px) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-250px) scale(0);
        opacity: 0;
    }
}

/* 已登录用户的星际导航页面 */
.space-dashboard {
    padding: 20px;
    background: rgba(10, 15, 30, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(100, 100, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.welcome-panel {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px;
    background: rgba(20, 30, 60, 0.5);
    border-radius: 15px;
    border: 1px solid rgba(100, 100, 255, 0.1);
}

.space-welcome {
    font-size: 2rem;
    background: linear-gradient(to right, #00c6ff, #a16bfe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
}

.space-welcome-sub {
    color: #a0a8c0;
    font-size: 1.1rem;
}

/* 星际导航 */
.space-navigation {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
}

.nav-planet {
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.nav-planet:hover {
    transform: scale(1.05);
}

.nav-planet-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(74, 78, 255, 0.3) 0%, rgba(156, 39, 176, 0.2) 50%, rgba(0, 0, 0, 0) 70%);
    filter: blur(10px);
    animation: navGlowPulse 3s ease-in-out infinite alternate;
}

@keyframes navGlowPulse {
    0% {
        opacity: 0.7;
        transform: scale(1);
    }
    100% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.nav-planet-content {
    position: relative;
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background: linear-gradient(135deg, #1a2a3a 0%, #0d1321 100%);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.8), inset 0 0 20px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    text-align: center;
    z-index: 1;
}

.nav-planet-content i {
    font-size: 2rem;
    color: #4a4eff;
    margin-bottom: 10px;
}

.nav-planet-content h3 {
    font-size: 1.2rem;
    color: white;
    margin-bottom: 5px;
}

.nav-planet-content p {
    font-size: 0.8rem;
    color: #a0a8c0;
}

/* 热门星际讨论 */
.space-discussions {
    margin-top: 40px;
}

.section-title {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
    display: inline-block;
    width: 100%;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(to right, transparent, #4a4eff, transparent);
}

.discussion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.discussion-card {
    position: relative;
    background: rgba(15, 20, 40, 0.7);
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    border: 1px solid rgba(100, 100, 255, 0.1);
}

.discussion-card:hover {
    transform: translateY(-5px);
}

.card-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 0%, rgba(74, 78, 255, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.discussion-card:hover .card-glow {
    opacity: 1;
}

.card-content {
    padding: 20px;
    position: relative;
    z-index: 1;
}

.card-content h4 {
    font-size: 1.1rem;
    color: white;
    margin-bottom: 10px;
}

.card-content p {
    font-size: 0.9rem;
    color: #a0a8c0;
    margin-bottom: 15px;
    line-height: 1.4;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6a7a9a;
}

.card-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.card-meta i {
    color: #4a4eff;
}

/* 导航栏星际风格 */
.navbar {
    background: rgba(10, 15, 30, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(100, 100, 255, 0.2);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 60px;
}

.nav-brand a {
    color: white;
    text-decoration: none;
    font-size: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(to right, #00c6ff, #a16bfe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.3s ease;
}

.nav-brand a:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-user, .nav-auth {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-text {
    color: #a0a8c0;
    font-size: 14px;
}

.nav-link {
    color: #a0a8c0;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-link:hover {
    color: white;
    background: rgba(74, 78, 255, 0.1);
    transform: translateY(-1px);
}

.register-btn {
    background: linear-gradient(45deg, #4a4eff, #9000ff);
    color: white !important;
    border-radius: 20px;
}

.register-btn:hover {
    background: linear-gradient(45deg, #5a5aff, #a000ff);
    box-shadow: 0 4px 15px rgba(74, 78, 255, 0.4);
}

/* 页脚样式 */
.footer {
    background: rgba(10, 15, 30, 0.8);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(100, 100, 255, 0.2);
    padding: 20px 0;
    margin-top: 40px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    color: #a0a8c0;
    font-size: 14px;
}

/* 主要内容区域 */
.main-content {
    min-height: calc(100vh - 140px);
}

/* 消息提示样式 */
.flash-messages {
    position: fixed;
    top: 70px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.flash-message {
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
    backdrop-filter: blur(10px);
}

.flash-success {
    background: rgba(76, 175, 80, 0.9);
    color: white;
    border-left: 4px solid #4CAF50;
}

.flash-error {
    background: rgba(244, 67, 54, 0.9);
    color: white;
    border-left: 4px solid #f44336;
}

.flash-close {
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    font-size: 16px;
    padding: 0;
    margin-left: 10px;
    transition: opacity 0.3s ease;
}

.flash-close:hover {
    opacity: 0.7;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 登录注册页面星际风格 */
.auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.auth-card {
    width: 100%;
    max-width: 400px;
    background: rgba(10, 15, 30, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(100, 100, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    padding: 30px;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-title {
    font-size: 2rem;
    background: linear-gradient(to right, #00c6ff, #a16bfe);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
}

.auth-subtitle {
    color: #a0a8c0;
    font-size: 1rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #a0a8c0;
    font-size: 0.9rem;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    background: rgba(20, 30, 60, 0.5);
    border: 1px solid rgba(100, 100, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #4a4eff;
    box-shadow: 0 0 0 2px rgba(74, 78, 255, 0.3);
}

.form-control::placeholder {
    color: #6a7a9a;
}

.auth-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #4a4eff, #9000ff);
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.auth-btn:hover {
    box-shadow: 0 6px 20px rgba(74, 78, 255, 0.4);
    transform: translateY(-2px);
}

.auth-footer {
    text-align: center;
    margin-top: 20px;
    color: #a0a8c0;
    font-size: 0.9rem;
}

.auth-link {
    color: #4a4eff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #a16bfe;
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .planet-container {
        height: 100vh;
        min-height: 500px;
        padding: 20px 10px;
    }
    
    .central-planet {
        width: min(350px, 85vw);
        height: min(350px, 85vw);
    }
    
    .orbit-1 {
        width: min(400px, 90vw);
        height: min(400px, 90vw);
    }
    
    .orbit-2 {
        width: min(500px, 100vw);
        height: min(500px, 100vw);
    }
    
    .orbit-3 {
        width: min(600px, 110vw);
        height: min(600px, 110vw);
    }
    
    .planet-surface {
        padding: 20px;
    }
    
    .space-actions {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }
    
    .space-btn {
        width: 100%;
        justify-content: center;
        padding: 15px 20px;
    }
    
    .space-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .space-navigation {
        flex-direction: column;
        align-items: center;
    }
    
    .discussion-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-container {
        padding: 0 15px;
    }
    
    .nav-menu {
        gap: 10px;
    }
    
    .nav-user, .nav-auth {
        gap: 10px;
    }
    
    .welcome-text {
        display: none;
    }
    
    .auth-card {
        max-width: 350px;
        padding: 25px;
    }
    
    .auth-title {
        font-size: 1.5rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .planet-container {
        min-height: 450px;
        padding: 15px 5px;
    }
    
    .central-planet {
        width: min(280px, 90vw);
        height: min(280px, 90vw);
    }
    
    .orbit-1 {
        width: min(320px, 95vw);
        height: min(320px, 95vw);
    }
    
    .orbit-2 {
        width: min(380px, 105vw);
        height: min(380px, 105vw);
    }
    
    .orbit-3 {
        width: min(440px, 115vw);
        height: min(440px, 115vw);
    }
    
    .planet-surface {
        padding: 15px;
    }
    
    .space-title {
        font-size: clamp(2rem, 10vw, 3rem);
    }
}