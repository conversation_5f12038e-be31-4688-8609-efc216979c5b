# 大学生论坛产品需求文档

## 1. Product Overview
大学生论坛是一个面向高校学生的在线交流平台，为不同学校的学生提供独立的讨论空间。
- 解决大学生校内信息交流不便、缺乏专属交流平台的问题，为在校学生提供便捷的信息分享和讨论环境。
- 目标是成为各高校学生首选的校园信息交流平台，促进校园文化建设和学生互动。

## 2. Core Features

### 2.1 User Roles
| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| 普通学生 | 邮箱注册+学校选择 | 在所属学校板块发帖、评论、浏览 |
| 管理员 | 系统分配 | 管理学校信息、用户管理、内容审核 |

### 2.2 Feature Module
我们的大学生论坛需求包含以下主要页面：
1. **首页**：学校列表展示、热门帖子推荐、导航菜单
2. **学校板块页**：该校帖子列表、发帖入口、板块信息
3. **帖子详情页**：帖子内容展示、评论区、用户信息
4. **发帖页面**：标题输入、内容编辑、图片上传、发布功能
5. **用户注册页**：用户名输入、密码设置、学校选择
6. **用户登录页**：登录表单、密码找回链接
7. **个人中心页**：个人信息展示、发帖历史、设置选项
8. **管理后台页**：用户管理、学校管理、内容审核

### 2.3 Page Details
| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 首页 | 学校列表 | 展示所有学校，点击进入对应学校板块 |
| 首页 | 热门推荐 | 显示各校热门帖子，增加平台活跃度 |
| 首页 | 用户导航 | 登录/注册入口，用户状态显示 |
| 学校板块页 | 帖子列表 | 显示该校所有帖子，支持时间排序 |
| 学校板块页 | 发帖入口 | 快速发帖按钮，仅限本校学生可见 |
| 学校板块页 | 搜索功能 | 在本校板块内搜索相关帖子 |
| 帖子详情页 | 内容展示 | 显示帖子标题、内容、图片、发布时间 |
| 帖子详情页 | 评论系统 | 用户评论、回复功能，评论时间显示 |
| 帖子详情页 | 用户信息 | 显示发帖人用户名、所属学校 |
| 发帖页面 | 内容编辑 | 标题输入框、富文本内容编辑器 |
| 发帖页面 | 图片上传 | 支持多图片上传，图片预览功能 |
| 发帖页面 | 发布控制 | 发布按钮、草稿保存、取消功能 |
| 用户注册页 | 注册表单 | 用户名、密码、确认密码、邮箱输入 |
| 用户注册页 | 学校选择 | 下拉列表选择所属学校 |
| 用户注册页 | 验证机制 | 邮箱验证、用户名重复检查 |
| 用户登录页 | 登录表单 | 用户名/邮箱、密码输入，记住登录 |
| 用户登录页 | 密码找回 | 通过邮箱重置密码功能 |
| 个人中心页 | 个人信息 | 显示用户名、邮箱、所属学校、注册时间 |
| 个人中心页 | 发帖历史 | 显示用户发布的所有帖子列表 |
| 个人中心页 | 设置选项 | 修改密码、邮箱设置、隐私设置 |
| 管理后台页 | 用户管理 | 查看用户列表、禁用/启用用户账号 |
| 管理后台页 | 学校管理 | 添加/编辑/删除学校信息 |
| 管理后台页 | 内容审核 | 审核举报内容、删除违规帖子 |

## 3. Core Process

**普通学生流程：**
用户首先访问首页查看学校列表，选择自己的学校进入对应板块。如果未登录，需要先注册账号并选择学校，然后登录。登录后可以在自己学校的板块内浏览帖子、发布新帖子、对帖子进行评论。用户也可以通过个人中心查看自己的发帖历史和修改个人设置。

**管理员流程：**
管理员通过后台系统登录，可以管理学校信息（添加新学校、编辑学校信息）、管理用户账号（查看用户列表、处理违规用户）、审核内容（处理用户举报、删除违规内容）。

```mermaid
graph TD
    A[首页] --> B[学校板块页]
    A --> C[用户注册页]
    A --> D[用户登录页]
    C --> D
    D --> A
    B --> E[帖子详情页]
    B --> F[发帖页面]
    F --> B
    E --> B
    D --> G[个人中心页]
    G --> A
    H[管理后台页] --> A
```

## 4. User Interface Design

### 4.1 Design Style
- **主色调**：蓝色系（#2196F3）作为主色，白色（#FFFFFF）作为背景色
- **辅助色**：灰色（#757575）用于次要文本，绿色（#4CAF50）用于成功提示
- **按钮样式**：圆角矩形按钮，主按钮使用主色调，次要按钮使用边框样式
- **字体**：中文使用微软雅黑，英文使用Arial，主标题16px，正文14px，辅助文本12px
- **布局风格**：卡片式布局，顶部导航栏，左侧可选侧边栏
- **图标风格**：使用简洁的线性图标，统一的视觉风格

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 首页 | 学校列表 | 网格布局的学校卡片，每个卡片显示学校名称和图标，悬停效果 |
| 首页 | 热门推荐 | 横向滚动的帖子卡片，显示标题、作者、学校、点赞数 |
| 首页 | 用户导航 | 顶部导航栏，右侧显示登录/用户头像，蓝色背景 |
| 学校板块页 | 帖子列表 | 垂直列表布局，每个帖子显示标题、摘要、作者、时间、评论数 |
| 学校板块页 | 发帖入口 | 固定在右下角的圆形发帖按钮，蓝色背景白色加号图标 |
| 帖子详情页 | 内容展示 | 白色背景卡片，标题使用大字体，内容区域支持图片展示 |
| 帖子详情页 | 评论系统 | 评论列表使用缩进显示层级，回复按钮使用小尺寸 |
| 发帖页面 | 内容编辑 | 简洁的表单设计，标题输入框和内容编辑器，工具栏在顶部 |
| 发帖页面 | 图片上传 | 拖拽上传区域，支持多图预览，删除按钮在图片右上角 |

### 4.3 Responsiveness
采用移动端优先的响应式设计，在手机端优化触摸交互体验，平板和桌面端提供更丰富的布局和功能展示。主要断点设置为768px和1024px，确保在不同设备上都有良好的用户体验。