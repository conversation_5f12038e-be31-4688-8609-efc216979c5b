{% extends "base.html" %}

{% block title %}{{ post.title }} - 大学生论坛{% endblock %}

{% block content %}
<!-- 帖子内容 -->
<div class="post-detail-section">
    <div class="card">
        <div class="card-body">
            <!-- 帖子标题 -->
            <h1 class="post-title" style="font-size: 24px; margin-bottom: 15px; color: #333;">
                {{ post.title }}
            </h1>
            
            <!-- 帖子元信息 -->
            <div class="post-meta" style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
                <span><i class="fas fa-user"></i> {{ post.username }}</span>
                <span><i class="fas fa-university"></i> {{ post.school_name }}</span>
                <span><i class="fas fa-clock"></i> {{ post.created_at }}</span>
                <span><i class="fas fa-eye"></i> {{ post.view_count }} 浏览</span>
            </div>
            
            <!-- 帖子内容 -->
            <div class="post-content" style="font-size: 16px; line-height: 1.8; margin-bottom: 30px;">
                {{ post.content|replace('\n', '<br>')|safe }}
            </div>
            
            <!-- 帖子图片 (如果有) -->
            {% if post.images %}
            <div class="post-images" style="margin-bottom: 30px;">
                {% set images = post.images|from_json if post.images else [] %}
                {% for image_url in images %}
                <img src="{{ image_url }}" alt="帖子图片" style="max-width: 100%; height: auto; margin-bottom: 10px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- 操作按钮 -->
            <div class="post-actions" style="margin-bottom: 20px;">
                <a href="{{ url_for('school_board', school_id=post.school_id) }}" class="btn btn-outline">
                    <i class="fas fa-arrow-left"></i> 返回{{ post.school_name }}
                </a>
                {% if current_user.is_authenticated %}
                <button class="btn btn-primary" onclick="showCommentForm()">
                    <i class="fas fa-comment"></i> 发表评论
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 评论区 -->
<div class="comments-section">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-comments"></i> 评论 ({{ comments|length }})
            </h2>
        </div>
        <div class="card-body">
            <!-- 评论表单 -->
            {% if current_user.is_authenticated %}
            <div id="comment-form" class="comment-form" style="display: none; margin-bottom: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                <h4 style="margin-bottom: 15px; color: #333;">
                    <i class="fas fa-edit"></i> 发表评论
                </h4>
                <form onsubmit="submitComment(event)">
                    <div class="form-group">
                        <textarea id="comment-content" class="form-control" rows="4" 
                                placeholder="写下你的评论..." required></textarea>
                    </div>
                    <div class="flex gap-10">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> 发表评论
                        </button>
                        <button type="button" class="btn btn-outline" onclick="hideCommentForm()">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    </div>
                </form>
            </div>
            {% else %}
            <div class="login-prompt" style="margin-bottom: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px; text-center;">
                <p style="color: #1976d2; margin-bottom: 15px;">
                    <i class="fas fa-info-circle"></i> 登录后即可发表评论
                </p>
                <div class="flex-center gap-10">
                    <a href="{{ url_for('login') }}" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </a>
                    <a href="{{ url_for('register') }}" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> 注册
                    </a>
                </div>
            </div>
            {% endif %}
            
            <!-- 评论列表 -->
            <div id="comments-list">
                {% if comments %}
                    {% for comment in comments %}
                    <div class="comment-item" data-comment-id="{{ comment.id }}">
                        <div class="comment-meta">
                            <span><i class="fas fa-user"></i> {{ comment.username }}</span>
                            <span><i class="fas fa-clock"></i> {{ comment.created_at }}</span>
                            {% if current_user.is_authenticated %}
                            <button class="reply-btn" onclick="showReplyForm({{ comment.id }})" 
                                    style="background: none; border: none; color: #2196F3; cursor: pointer; font-size: 12px;">
                                <i class="fas fa-reply"></i> 回复
                            </button>
                            {% endif %}
                        </div>
                        <div class="comment-content">
                            {{ comment.content|replace('\n', '<br>')|safe }}
                        </div>
                        
                        <!-- 回复表单 -->
                        {% if current_user.is_authenticated %}
                        <div id="reply-form-{{ comment.id }}" class="reply-form" style="display: none; margin-top: 15px; padding: 15px; background: #f0f0f0; border-radius: 6px;">
                            <form onsubmit="submitReply(event, {{ comment.id }})">
                                <div class="form-group">
                                    <textarea class="form-control" rows="3" placeholder="回复 {{ comment.username }}..." required></textarea>
                                </div>
                                <div class="flex gap-10">
                                    <button type="submit" class="btn btn-primary" style="font-size: 12px; padding: 6px 12px;">
                                        <i class="fas fa-paper-plane"></i> 回复
                                    </button>
                                    <button type="button" class="btn btn-outline" onclick="hideReplyForm({{ comment.id }})" 
                                            style="font-size: 12px; padding: 6px 12px;">
                                        取消
                                    </button>
                                </div>
                            </form>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="no-comments text-center" style="padding: 40px 20px; color: #666;">
                        <i class="fas fa-comment-slash" style="font-size: 48px; margin-bottom: 15px; color: #ddd;"></i>
                        <p>还没有评论，快来发表第一个评论吧！</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// 显示评论表单
function showCommentForm() {
    document.getElementById('comment-form').style.display = 'block';
    document.getElementById('comment-content').focus();
}

// 隐藏评论表单
function hideCommentForm() {
    document.getElementById('comment-form').style.display = 'none';
    document.getElementById('comment-content').value = '';
}

// 提交评论
function submitComment(event) {
    event.preventDefault();
    const content = document.getElementById('comment-content').value.trim();
    
    if (!content) {
        alert('请输入评论内容');
        return;
    }
    
    fetch('/api/comment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            content: content,
            post_id: {{ post.id }}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 刷新页面显示新评论
            location.reload();
        } else {
            alert(data.message || '评论失败，请重试');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('评论失败，请重试');
    });
}

// 显示回复表单
function showReplyForm(commentId) {
    // 隐藏所有回复表单
    document.querySelectorAll('.reply-form').forEach(form => {
        form.style.display = 'none';
    });
    
    // 显示指定的回复表单
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    replyForm.style.display = 'block';
    replyForm.querySelector('textarea').focus();
}

// 隐藏回复表单
function hideReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    replyForm.style.display = 'none';
    replyForm.querySelector('textarea').value = '';
}

// 提交回复
function submitReply(event, parentId) {
    event.preventDefault();
    const form = event.target;
    const content = form.querySelector('textarea').value.trim();
    
    if (!content) {
        alert('请输入回复内容');
        return;
    }
    
    fetch('/api/comment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            content: content,
            post_id: {{ post.id }},
            parent_id: parentId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 刷新页面显示新回复
            location.reload();
        } else {
            alert(data.message || '回复失败，请重试');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('回复失败，请重试');
    });
}

// 页面加载完成后自动显示评论表单（如果URL中有hash）
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hash === '#comment') {
        showCommentForm();
    }
});
</script>
{% endblock %}