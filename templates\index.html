{% extends "base.html" %}

{% block title %}星际论坛 - 探索无限宇宙{% endblock %}

{% block content %}
<!-- 螺旋星系主页容器 -->
<div class="space-container">
    <!-- 螺旋星系背景 -->
    <div class="spiral-galaxy-bg">
        <!-- 主螺旋星系 -->
        <div class="main-spiral">
            <div class="spiral-rings">
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
            </div>
            <div class="spiral-particles" id="spiralParticles"></div>
        </div>
        
        <!-- 第二层螺旋星系 -->
        <div class="secondary-spiral">
            <div id="secondaryParticles"></div>
        </div>
        
        <!-- 浮动星尘 -->
        <div id="floatingStardust"></div>
        
        <!-- 流星效果 -->
        <div id="meteors"></div>
        
        <!-- 径向渐变覆盖层 -->
        <div class="radial-overlay"></div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="space-content">
        {% if not current_user.is_authenticated %}
        <!-- 未登录用户看到的螺旋星系欢迎页面 -->
        <div class="text-center">
            <!-- 论坛标题 -->
            <div class="space-header">
                <h1 class="space-title">星际论坛</h1>
                <div class="title-divider"></div>
            </div>
            
            <!-- 副标题 -->
            <p class="space-subtitle max-w-2xl mx-auto leading-relaxed">
                探索无限宇宙，连接每一颗星球上的智慧生命
            </p>
            <p class="space-description max-w-2xl mx-auto">
                在这里，每个想法都能照亮黑暗的太空
            </p>
            
            <!-- 操作按钮 -->
            <div class="space-buttons">
                <a href="{{ url_for('register') }}" class="modern-btn modern-btn-primary">
                    <span>
                        <i class="fas fa-rocket"></i> 开始探索
                    </span>
                </a>
                <a href="{{ url_for('login') }}" class="modern-btn modern-btn-secondary">
                    <span>
                        <i class="fas fa-satellite"></i> 发现登录
                    </span>
                </a>
            </div>
            
            <!-- 统计数据 -->
            <div class="space-stats">
                <div class="stat-item">
                    <div class="stat-number">3,247</div>
                    <div class="stat-label">活跃星球</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">186K</div>
                    <div class="stat-label">星际探索者</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1.2M</div>
                    <div class="stat-label">宇宙对话</div>
                </div>
            </div>
        </div>
        
        {% else %}
        <!-- 已登录用户看到的星际导航页面 -->
        <div class="space-dashboard">
            <div class="welcome-panel">
                <h2 class="space-welcome">欢迎回到星际，{{ current_user.username }}！</h2>
                <p class="space-welcome-sub">准备好继续你的宇宙探索之旅了吗？</p>
            </div>
            
            <!-- 星际导航 -->
            <div class="space-navigation">
                <div class="nav-planet" onclick="location.href='{{ url_for('schools_list') }}'">
                    <div class="nav-planet-glow"></div>
                    <div class="nav-planet-content">
                        <i class="fas fa-university"></i>
                        <h3>星球联盟</h3>
                        <p>探索各个学院星球</p>
                    </div>
                </div>
                
                <div class="nav-planet" onclick="location.href='{{ url_for('create_post') }}'">
                    <div class="nav-planet-glow"></div>
                    <div class="nav-planet-content">
                        <i class="fas fa-pen"></i>
                        <h3>发射信号</h3>
                        <p>向宇宙发送你的想法</p>
                    </div>
                </div>
                
                <div class="nav-planet" onclick="location.href='{{ url_for('profile') }}'">
                    <div class="nav-planet-glow"></div>
                    <div class="nav-planet-content">
                        <i class="fas fa-user-astronaut"></i>
                        <h3>个人空间站</h3>
                        <p>管理你的星际档案</p>
                    </div>
                </div>
            </div>
            
            <!-- 热门星际讨论 -->
            {% if hot_posts %}
            <div class="space-discussions">
                <h3 class="section-title">热门星际讨论</h3>
                <div class="discussion-grid">
                    {% for post in hot_posts[:6] %}
                    <div class="discussion-card" onclick="location.href='{{ url_for('post_detail', post_id=post.id) }}'">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h4>{{ post.title }}</h4>
                            <p>{{ post.content[:100] }}{% if post.content|length > 100 %}...{% endif %}</p>
                            <div class="card-meta">
                                <span><i class="fas fa-user"></i> {{ post.username }}</span>
                                <span><i class="fas fa-eye"></i> {{ post.view_count }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- 粒子效果 -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>
{% endblock %}