/* 螺旋星系主题样式 - 基于新设计 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #e1e6fa;
    background-color: #000;
    min-height: 100vh;
    overflow-x: hidden;
}

/* 导航栏样式 */
.navbar {
    background: rgba(17, 24, 39, 0.9);
    backdrop-filter: blur(16px);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
    color: white;
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 70px;
}

.nav-brand a {
    color: white;
    text-decoration: none;
    font-size: 22px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(45deg, #06b6d4, #9333ea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.nav-brand a:hover {
    transform: scale(1.05);
}

.nav-brand i {
    color: #06b6d4;
    font-size: 20px;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-user, .nav-auth {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-text {
    color: #d1d5db;
    font-size: 14px;
    font-weight: 500;
}

.nav-link {
    color: #d1d5db;
    text-decoration: none;
    padding: 10px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    border: 1px solid transparent;
}

.nav-link:hover {
    background: rgba(6, 182, 212, 0.1);
    border-color: rgba(6, 182, 212, 0.3);
    color: #06b6d4;
    transform: translateY(-1px);
}

.register-btn {
    background: linear-gradient(45deg, #9333ea, #ec4899);
    color: white !important;
    border: none;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.register-btn:hover {
    background: linear-gradient(45deg, #7c3aed, #db2777);
    box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4);
    color: white !important;
}

/* 消息提示样式 */
.flash-messages {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.flash-message {
    padding: 16px 20px;
    margin-bottom: 12px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease-out;
    backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.flash-success {
    background: rgba(34, 197, 94, 0.9);
    color: white;
    border-left: 4px solid #22c55e;
}

.flash-error {
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border-left: 4px solid #ef4444;
}

.flash-close {
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    font-size: 16px;
    padding: 0;
    margin-left: 12px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.flash-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 主要内容区域 */
.main-content {
    position: relative;
    z-index: 1;
}

/* 页脚样式 */
.footer {
    background: rgba(17, 24, 39, 0.9);
    backdrop-filter: blur(16px);
    border-top: 1px solid rgba(75, 85, 99, 0.3);
    color: #d1d5db;
    text-align: center;
    padding: 30px 0;
    margin-top: 60px;
    position: relative;
    z-index: 1;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 螺旋星系背景容器 */
.spiral-galaxy-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

/* 主螺旋星系 */
.main-spiral {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 800px;
    height: 800px;
    animation: spiralRotate 60s linear infinite;
}

/* 螺旋臂环 */
.spiral-rings {
    position: absolute;
    inset: 0;
    opacity: 0.8;
}

.spiral-ring {
    position: absolute;
    border-radius: 50%;
    border: 3px solid;
    animation: ringPulse 4s ease-in-out infinite;
}

.spiral-ring:nth-child(1) {
    width: 100%;
    height: 100%;
    border-color: rgba(59, 130, 246, 0.3);
    animation-delay: 0s;
}

.spiral-ring:nth-child(2) {
    width: 83.33%;
    height: 83.33%;
    top: 8.33%;
    left: 8.33%;
    border-color: rgba(147, 51, 234, 0.4);
    animation-delay: 1s;
}

.spiral-ring:nth-child(3) {
    width: 66.67%;
    height: 66.67%;
    top: 16.67%;
    left: 16.67%;
    border-color: rgba(6, 182, 212, 0.5);
    animation-delay: 2s;
}

.spiral-ring:nth-child(4) {
    width: 50%;
    height: 50%;
    top: 25%;
    left: 25%;
    border-color: rgba(236, 72, 153, 0.6);
    animation-delay: 3s;
}

/* 螺旋粒子点 */
.spiral-particles {
    position: absolute;
    inset: 0;
}

.spiral-particle {
    position: absolute;
    width: 1px;
    height: 1px;
    background: white;
    border-radius: 50%;
    animation: particlePulse 3s ease-in-out infinite;
}

/* 第二层螺旋星系 */
.secondary-spiral {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1200px;
    height: 1200px;
    animation: spiralRotate 90s linear infinite reverse;
}

.secondary-particle {
    position: absolute;
    width: 0.5px;
    height: 0.5px;
    background: linear-gradient(45deg, #3b82f6, #9333ea);
    border-radius: 50%;
    animation: particlePulse 4s ease-in-out infinite;
    box-shadow: 0 0 2px #4f46e5;
}

/* 浮动星尘粒子 */
.floating-stardust {
    position: absolute;
    width: 1px;
    height: 1px;
    background: white;
    border-radius: 50%;
    animation: stardustFloat 15s linear infinite;
    opacity: 0.3;
}

/* 流星效果 */
.meteor {
    position: absolute;
    width: 1px;
    height: 1px;
    background: linear-gradient(45deg, white, transparent);
    border-radius: 50%;
    transform: rotate(-45deg);
    box-shadow: 0 0 20px 2px rgba(255,255,255,0.8), 0 0 40px 4px rgba(100,200,255,0.3);
    animation: meteorFly 4s linear infinite;
}

/* 径向渐变覆盖层 */
.radial-overlay {
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 50% 50%, 
        rgba(0,0,0,0) 0%, 
        rgba(30,20,60,0.3) 40%, 
        rgba(0,0,0,0.8) 100%);
    opacity: 0.6;
    pointer-events: none;
}

/* 动画定义 */
@keyframes spiralRotate {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes ringPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes particlePulse {
    0%, 100% {
        opacity: 0.2;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.5);
    }
}

@keyframes stardustFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.3;
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(-100px) translateX(50px);
        opacity: 0;
    }
}

@keyframes meteorFly {
    0% {
        transform: rotate(-45deg) translateX(-100px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: rotate(-45deg) translateX(calc(100vw + 100px));
        opacity: 0;
    }
}

/* 主容器 */
.space-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    position: relative;
    z-index: 1;
}

.space-content {
    width: 100%;
    max-width: 1200px;
    z-index: 2;
}

/* 现代化卡片设计 */
.modern-card {
    background: rgba(17, 24, 39, 0.6);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(75, 85, 99, 0.6);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
}

.modern-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, transparent 50%, rgba(147, 51, 234, 0.05) 100%);
    pointer-events: none;
}

.modern-card .card-glow-top {
    position: absolute;
    top: -112px;
    right: -112px;
    width: 224px;
    height: 224px;
    background: radial-gradient(circle, rgba(6, 182, 212, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: cardGlowPulse 4s ease-in-out infinite;
}

.modern-card .card-glow-bottom {
    position: absolute;
    bottom: -112px;
    left: -112px;
    width: 224px;
    height: 224px;
    background: radial-gradient(circle, rgba(147, 51, 234, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: cardGlowPulse 4s ease-in-out infinite;
    animation-delay: 1s;
}

@keyframes cardGlowPulse {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

/* 现代化按钮 */
.modern-btn {
    position: relative;
    padding: 12px 32px;
    border-radius: 9999px;
    font-weight: 600;
    font-size: 18px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    overflow: hidden;
    cursor: pointer;
    border: none;
}

.modern-btn-primary {
    background: linear-gradient(45deg, #3b82f6, #9333ea);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.modern-btn-primary:hover {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.6);
    transform: translateY(-2px) scale(1.05);
}

.modern-btn-primary::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, #9333ea, #ec4899);
    transform: scaleX(0);
    transition: transform 0.3s ease;
    transform-origin: left;
}

.modern-btn-primary:hover::before {
    transform: scaleX(1);
}

.modern-btn-secondary {
    background: transparent;
    color: #06b6d4;
    border: 2px solid #06b6d4;
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.2);
}

.modern-btn-secondary:hover {
    background: rgba(6, 182, 212, 0.1);
    box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
    transform: translateY(-2px) scale(1.05);
}

.modern-btn span {
    position: relative;
    z-index: 1;
}

/* 按钮容器 */
.space-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    justify-content: center;
    align-items: center;
    margin-bottom: 64px;
    padding: 0 20px;
}

@media (min-width: 768px) {
    .space-buttons {
        flex-direction: row;
        gap: 32px;
        margin-bottom: 80px;
    }
}

/* 标题样式 */
.space-header {
    text-align: center;
    margin-bottom: 48px;
}

.space-title {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 800;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #06b6d4, #9333ea, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titlePulse 3s ease-in-out infinite;
    text-align: center;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.title-divider {
    height: 4px;
    width: 160px;
    margin: 0 auto;
    background: linear-gradient(90deg, transparent, #06b6d4, #9333ea, #ec4899, transparent);
    border-radius: 2px;
    animation: dividerPulse 3s ease-in-out infinite;
}

@keyframes dividerPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scaleX(1);
    }
    50% {
        opacity: 1;
        transform: scaleX(1.1);
    }
}

@keyframes titlePulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

.space-subtitle {
    font-size: clamp(1.25rem, 3vw, 2rem);
    color: #d1d5db;
    margin-bottom: 48px;
    text-align: center;
    line-height: 1.4;
}

.space-description {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
    color: #06b6d4;
    text-align: center;
    margin-bottom: 48px;
    animation: descriptionPulse 4s ease-in-out infinite;
}

@keyframes descriptionPulse {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

/* 统计数据 */
.space-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-top: 80px;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.stat-item {
    transition: all 0.3s ease;
    cursor: pointer;
    padding: 20px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(147, 51, 234, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-number {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 800;
    margin-bottom: 12px;
    animation: statPulse 3s ease-in-out infinite;
    position: relative;
    z-index: 1;
    line-height: 1;
}

.stat-item:nth-child(1) .stat-number {
    background: linear-gradient(45deg, #06b6d4, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-item:nth-child(2) .stat-number {
    background: linear-gradient(45deg, #9333ea, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation-delay: 1s;
}

.stat-item:nth-child(3) .stat-number {
    background: linear-gradient(45deg, #ec4899, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation-delay: 2s;
}

.stat-label {
    font-size: clamp(0.9rem, 2.5vw, 1.1rem);
    color: #d1d5db;
    transition: color 0.3s ease;
    font-weight: 500;
    position: relative;
    z-index: 1;
    line-height: 1.2;
}

.stat-item:hover .stat-label {
    color: #ffffff;
}

@keyframes statPulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-spiral {
        width: 600px;
        height: 600px;
    }

    .secondary-spiral {
        width: 900px;
        height: 900px;
    }

    .space-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-top: 50px;
        padding: 0 10px;
        max-width: 100%;
    }

    .stat-item {
        padding: 16px 12px;
        margin: 0;
        min-height: auto;
    }

    .stat-number {
        font-size: clamp(2.2rem, 8vw, 3.5rem);
        margin-bottom: 10px;
    }

    .stat-label {
        font-size: clamp(0.95rem, 3vw, 1.1rem);
    }

    .modern-card {
        padding: 24px;
    }

    .space-title {
        font-size: clamp(2.5rem, 10vw, 4.5rem);
        margin-bottom: 20px;
    }

    .space-subtitle {
        font-size: clamp(1.1rem, 4vw, 1.6rem);
        margin-bottom: 32px;
    }

    .space-description {
        font-size: clamp(0.95rem, 3vw, 1.1rem);
        margin-bottom: 40px;
    }
}

@media (max-width: 480px) {
    .main-spiral {
        width: 400px;
        height: 400px;
    }

    .secondary-spiral {
        width: 600px;
        height: 600px;
    }

    .space-stats {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 40px;
        padding: 0 20px;
        max-width: 320px;
    }

    .stat-item {
        padding: 20px 16px;
        max-width: 100%;
        margin: 0 auto;
    }

    .stat-number {
        font-size: clamp(2.2rem, 8vw, 3.2rem);
        margin-bottom: 8px;
    }

    .stat-label {
        font-size: clamp(0.95rem, 3.5vw, 1.1rem);
    }

    .modern-btn {
        padding: 12px 28px;
        font-size: 16px;
        width: 100%;
        max-width: 200px;
    }

    .space-container {
        padding: 15px;
    }

    .space-title {
        font-size: clamp(2rem, 12vw, 3.5rem);
        margin-bottom: 16px;
    }

    .space-subtitle {
        font-size: clamp(1rem, 5vw, 1.4rem);
        margin-bottom: 24px;
    }

    .space-description {
        font-size: clamp(0.9rem, 4vw, 1rem);
        margin-bottom: 32px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
    .space-stats {
        gap: 16px;
        margin-top: 35px;
        padding: 0 15px;
        max-width: 280px;
    }

    .stat-item {
        padding: 16px 12px;
    }

    .stat-number {
        font-size: clamp(1.8rem, 9vw, 2.8rem);
        margin-bottom: 6px;
    }

    .stat-label {
        font-size: clamp(0.85rem, 4vw, 1rem);
    }

    .space-title {
        font-size: clamp(1.8rem, 14vw, 3rem);
        margin-bottom: 12px;
    }

    .title-divider {
        width: 120px;
        height: 3px;
    }

    .space-subtitle {
        font-size: clamp(0.95rem, 5.5vw, 1.3rem);
        margin-bottom: 20px;
    }

    .space-description {
        font-size: clamp(0.85rem, 4.5vw, 0.95rem);
        margin-bottom: 28px;
    }

    .modern-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    /* 导航栏响应式 */
    .nav-container {
        padding: 0 15px;
        height: 60px;
    }

    .nav-brand a {
        font-size: 18px;
    }

    .nav-brand i {
        font-size: 16px;
    }

    .nav-menu {
        gap: 12px;
    }

    .nav-user, .nav-auth {
        gap: 10px;
    }

    .nav-link {
        padding: 8px 12px;
        font-size: 13px;
    }

    .welcome-text {
        font-size: 12px;
    }

    .flash-messages {
        right: 15px;
        left: 15px;
        max-width: none;
    }
}