
'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function LoginPage() {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 这里后续可以添加登录逻辑
    console.log('登录数据:', formData);
  };

  return (
    <div className="relative min-h-screen overflow-hidden bg-black flex items-center justify-center">
      {/* 增强版星空背景效果 */}
      <div className="absolute inset-0">
        {/* 主要螺旋星系背景 - 增大尺寸 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="relative w-[900px] h-[900px] animate-spin opacity-30" style={{ animationDuration: '80s' }}>
            {/* 多层螺旋环 - 增大尺寸 */}
            <div className="absolute inset-0">
              <div className="absolute w-full h-full rounded-full border-3 border-cyan-400/20 animate-pulse"></div>
              <div className="absolute w-4/5 h-4/5 top-[10%] left-[10%] rounded-full border-3 border-blue-400/25 animate-pulse" style={{ animationDelay: '1s' }}></div>
              <div className="absolute w-3/5 h-3/5 top-[20%] left-[20%] rounded-full border-3 border-purple-400/30 animate-pulse" style={{ animationDelay: '2s' }}></div>
              <div className="absolute w-2/5 h-2/5 top-[30%] left-[30%] rounded-full border-3 border-pink-400/35 animate-pulse" style={{ animationDelay: '3s' }}></div>
              <div className="absolute w-1/5 h-1/5 top-[40%] left-[40%] rounded-full border-2 border-yellow-400/40 animate-pulse" style={{ animationDelay: '4s' }}></div>
            </div>
            
            {/* 螺旋粒子 - 增加数量和调整分布 */}
            {Array.from({ length: 100 }).map((_, i) => {
              const angle = (i * 12) % 360;
              const radius = 80 + (i * 5);
              const x = Math.cos((angle * Math.PI) / 180) * radius;
              const y = Math.sin((angle * Math.PI) / 180) * radius;
              return (
                <div
                  key={i}
                  className="absolute w-1.5 h-1.5 bg-white rounded-full animate-pulse"
                  style={{
                    left: `calc(50% + ${x}px)`,
                    top: `calc(50% + ${y}px)`,
                    animationDelay: `${i * 0.08}s`,
                    opacity: 0.3 + ((i % 3) * 0.2),
                    boxShadow: `0 0 ${3 + (i % 4)}px ${['#00ffff', '#ff00ff', '#ffff00', '#ff6b6b'][i % 4]}`
                  }}
                />
              );
            })}
          </div>
        </div>

        {/* 流动的星尘 */}
        {Array.from({ length: 150 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-px h-px bg-white rounded-full animate-pulse"
            style={{
              left: `${(i * 7) % 100}%`,
              top: `${(i * 11) % 100}%`,
              animationDelay: `${(i % 15) * 0.3}s`,
              animationDuration: `${2 + (i % 3)}s`,
              opacity: 0.2 + ((i % 4) * 0.15)
            }}
          />
        ))}

        {/* 慢速移动的大光点 - 增大尺寸 */}
        {Array.from({ length: 25 }).map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full animate-pulse"
            style={{
              left: `${(i * 13) % 100}%`,
              top: `${(i * 17) % 100}%`,
              width: `${4 + (i % 4)}px`,
              height: `${4 + (i % 4)}px`,
              background: `radial-gradient(circle, ${['#00ffff', '#ff00ff', '#ffff00', '#ff6b6b'][i % 4]} 0%, transparent 70%)`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${3 + (i % 2)}s`
            }}
          />
        ))}

        {/* 增强的流星雨 */}
        {Array.from({ length: 12 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-white to-transparent rounded-full"
            style={{
              left: `${(i * 11) % 100}%`,
              top: `${(i * 19) % 100}%`,
              animation: `meteor ${3 + (i % 3)}s linear infinite`,
              animationDelay: `${i * 0.4}s`,
              transform: 'rotate(-45deg)',
              boxShadow: '0 0 15px 1px rgba(255,255,255,0.6), 0 0 30px 2px rgba(0,255,255,0.3)',
            }}
          />
        ))}
        
        {/* 多层渐变覆盖 */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-purple-900/15 to-black/40" />
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-black/20 to-black/60" />
      </div>

      {/* 登录表单容器 */}
      <div className="relative z-10 w-full max-w-md mx-4">
        {/* 返回首页按钮 */}
        <Link 
          href="/" 
          className="inline-flex items-center text-cyan-400 hover:text-cyan-300 transition-colors duration-300 mb-8 group cursor-pointer"
        >
          <i className="ri-arrow-left-line mr-2 text-xl w-6 h-6 flex items-center justify-center group-hover:-translate-x-1 transition-transform duration-300"></i>
          返回首页
        </Link>

        {/* 登录卡片 */}
        <div className="bg-gray-900/60 backdrop-blur-md border border-gray-700/60 rounded-2xl p-8 shadow-2xl relative overflow-hidden">
          {/* 卡片内部光效 - 增大尺寸 */}
          <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/5 via-transparent to-purple-400/5 pointer-events-none"></div>
          <div className="absolute -top-28 -right-28 w-56 h-56 bg-gradient-radial from-cyan-400/10 to-transparent rounded-full animate-pulse"></div>
          <div className="absolute -bottom-28 -left-28 w-56 h-56 bg-gradient-radial from-purple-400/10 to-transparent rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
          
          <div className="relative z-10">
            {/* 标题 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-500 mb-2 font-['Pacifico'] animate-pulse">
                欢迎回来！
              </h1>
              <p className="text-gray-300">请登录您的账户</p>
            </div>

            {/* 登录表单 */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* 用户名/邮箱输入框 */}
              <div className="group">
                <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2 group-focus-within:text-cyan-400 transition-colors duration-300">
                  用户名/邮箱
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="ri-user-line text-gray-400 w-5 h-5 flex items-center justify-center group-focus-within:text-cyan-400 transition-colors duration-300"></i>
                  </div>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400/50 transition-all duration-300 text-sm backdrop-blur-sm"
                    placeholder="请输入用户名或邮箱"
                    required
                  />
                </div>
              </div>

              {/* 密码输入框 */}
              <div className="group">
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2 group-focus-within:text-cyan-400 transition-colors duration-300">
                  密码
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i className="ri-lock-line text-gray-400 w-5 h-5 flex items-center justify-center group-focus-within:text-cyan-400 transition-colors duration-300"></i>
                  </div>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400/50 transition-all duration-300 text-sm backdrop-blur-sm"
                    placeholder="请输入密码"
                    required
                  />
                </div>
              </div>

              {/* 忘记密码链接 */}
              <div className="text-right">
                <Link 
                  href="/forgot-password" 
                  className="text-sm text-cyan-400 hover:text-cyan-300 transition-colors duration-300 cursor-pointer hover:underline"
                >
                  忘记密码？
                </Link>
              </div>

              {/* 登录按钮 */}
              <button
                type="submit"
                className="w-full py-3 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg text-white font-semibold text-lg hover:from-cyan-600 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-cyan-500/40 whitespace-nowrap cursor-pointer relative overflow-hidden group"
              >
                <span className="relative z-10 flex items-center justify-center">
                  <i className="ri-login-circle-line mr-2 w-5 h-5 inline-flex items-center justify-center"></i>
                  登录
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </button>
            </form>

            {/* 注册链接 */}
            <div className="mt-8 text-center">
              <p className="text-gray-300">
                没有账户？
                <Link 
                  href="/register" 
                  className="text-cyan-400 hover:text-cyan-300 transition-colors duration-300 ml-2 cursor-pointer hover:underline"
                >
                  点击这里注册
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CSS 动画定义 */}
      <style jsx>{`
        @keyframes meteor {
          0% {
            transform: rotate(-45deg) translateX(-100px);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: rotate(-45deg) translateX(calc(100vw + 100px));
            opacity: 0;
          }
        }
        
        .bg-gradient-radial {
          background: radial-gradient(circle, var(--tw-gradient-stops));
        }
      `}</style>
    </div>
  );
}