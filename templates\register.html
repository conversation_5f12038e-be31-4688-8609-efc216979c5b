{% extends "base.html" %}

{% block title %}创建新账户 - 星际论坛{% endblock %}

{% block content %}
<!-- 星际注册页面 -->
<div class="space-container">
    <!-- 螺旋星系背景 -->
    <div class="spiral-galaxy-bg">
        <!-- 主螺旋星系 -->
        <div class="main-spiral">
            <div class="spiral-rings">
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
                <div class="spiral-ring"></div>
            </div>
            <div class="spiral-particles" id="spiralParticles"></div>
        </div>

        <!-- 第二层螺旋星系 -->
        <div class="secondary-spiral">
            <div id="secondaryParticles"></div>
        </div>

        <!-- 浮动星尘 -->
        <div id="floatingStardust"></div>

        <!-- 流星效果 -->
        <div id="meteors"></div>

        <!-- 径向渐变覆盖层 -->
        <div class="radial-overlay"></div>
    </div>

    <div class="space-content flex items-center justify-center min-h-screen py-12">
        <div class="w-full max-w-lg mx-4">
            <!-- 注册卡片 -->
            <div class="modern-card">
                <div class="card-glow-top"></div>
                <div class="card-glow-bottom"></div>

                <div class="relative z-10">
            <!-- 返回首页链接 -->
            <div style="margin-bottom: 20px;">
                <a href="{{ url_for('index') }}" style="color: #4a4eff; text-decoration: none; display: flex; align-items: center; gap: 8px; font-size: 14px;">
                    <i class="fas fa-arrow-left"></i> 返回首页
                </a>
            </div>
            
            <!-- 注册头部 -->
            <div class="auth-header">
                <h1 class="auth-title">创建新账户</h1>
                <p class="auth-subtitle">加入星际论坛大家庭</p>
            </div>
            
            <!-- 注册表单 -->
            <form method="POST" action="{{ url_for('register') }}">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" id="username" name="username" class="form-control" 
                           placeholder="请输入用户名" required maxlength="50">
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">邮箱</label>
                    <input type="email" id="email" name="email" class="form-control" 
                           placeholder="请输入邮箱地址" required>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" id="password" name="password" class="form-control" 
                           placeholder="请输入密码 (至少6位)" required minlength="6">
                </div>
                
                <div class="form-group">
                    <label for="confirm_password" class="form-label">确认密码</label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                           placeholder="请再次输入密码" required minlength="6">
                </div>
                
                <div class="form-group">
                    <label for="school_id" class="form-label">选择学校</label>
                    <div class="school-selector">
                        <!-- 搜索框 -->
                        <div class="search-box" style="position: relative; margin-bottom: 10px;">
                            <input type="text" 
                                   id="school-search" 
                                   placeholder="搜索学校名称..." 
                                   class="form-control"
                                   style="padding-right: 40px;">
                            <i class="fas fa-search" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #6a7a9a;"></i>
                        </div>
                        
                        <!-- 筛选选项 -->
                        <div class="filter-options" style="display: flex; gap: 10px; margin-bottom: 10px;">
                            <select id="province-filter" class="form-control" style="flex: 1;">
                                <option value="">全部省份</option>
                            </select>
                            <select id="type-filter" class="form-control" style="flex: 1;">
                                <option value="">全部类型</option>
                            </select>
                        </div>
                        
                        <!-- 学校选择下拉框 -->
                        <select id="school_id" name="school_id" class="form-control" required>
                            <option value="">请选择你的学校</option>
                            {% for school in schools %}
                            <option value="{{ school.id }}" 
                                    data-province="{{ school.province or '' }}" 
                                    data-city="{{ school.city or '' }}" 
                                    data-type="{{ school.school_type or '' }}">
                                {{ school.name }}
                                {% if school.province and school.city %} - {{ school.province }} {{ school.city }}{% endif %}
                                {% if school.school_type %} ({{ school.school_type }}){% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        
                        <!-- 学校信息显示 -->
                        <div id="school-info" class="school-info" style="display: none; margin-top: 10px; padding: 10px; background: rgba(20, 30, 60, 0.3); border-radius: 8px; border: 1px solid rgba(100, 100, 255, 0.1);">
                            <div class="school-details">
                                <h4 id="selected-school-name" style="color: white; margin-bottom: 5px; font-size: 1rem;"></h4>
                                <p id="selected-school-location" style="color: #a0a8c0; margin-bottom: 3px; font-size: 0.9rem;"></p>
                                <p id="selected-school-type" style="color: #4a4eff; margin: 0; font-size: 0.8rem;"></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="auth-btn">
                    <i class="fas fa-user-astronaut"></i> 注册
                </button>
            </form>
            
            <!-- 登录链接 -->
            <div class="auth-footer">
                <span>已有账户？</span>
                <a href="{{ url_for('login') }}" class="auth-link">点击登录</a>
            </div>
        </div>
    </div>
</div>

<!-- 粒子效果 -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>

<script>
// 密码确认验证
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('两次输入的密码不一致');
    } else {
        this.setCustomValidity('');
    }
});

// 密码强度提示
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const confirmPassword = document.getElementById('confirm_password');
    
    // 重新验证确认密码
    if (confirmPassword.value && password !== confirmPassword.value) {
        confirmPassword.setCustomValidity('两次输入的密码不一致');
    } else {
        confirmPassword.setCustomValidity('');
    }
});

// 学校搜索功能
document.getElementById('school-search').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const schoolSelect = document.getElementById('school_id');
    const options = schoolSelect.querySelectorAll('option');
    
    options.forEach(option => {
        if (option.value === '') return; // 跳过默认选项
        
        const schoolName = option.textContent.toLowerCase();
        if (schoolName.includes(searchTerm)) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
});

// 学校选择显示信息
document.getElementById('school_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const schoolInfo = document.getElementById('school-info');
    
    if (selectedOption.value) {
        const schoolName = selectedOption.textContent.split(' - ')[0].split(' (')[0];
        const province = selectedOption.getAttribute('data-province');
        const city = selectedOption.getAttribute('data-city');
        const type = selectedOption.getAttribute('data-type');
        
        document.getElementById('selected-school-name').textContent = schoolName;
        document.getElementById('selected-school-location').textContent = 
            province && city ? `${province} ${city}` : '';
        document.getElementById('selected-school-type').textContent = 
            type ? `类型: ${type}` : '';
        
        schoolInfo.style.display = 'block';
    } else {
        schoolInfo.style.display = 'none';
    }
});

// 加载省份和类型筛选选项
fetch('/api/provinces')
    .then(response => response.json())
    .then(provinces => {
        const provinceFilter = document.getElementById('province-filter');
        provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province;
            option.textContent = province;
            provinceFilter.appendChild(option);
        });
    })
    .catch(error => console.log('加载省份数据失败:', error));

fetch('/api/school_types')
    .then(response => response.json())
    .then(types => {
        const typeFilter = document.getElementById('type-filter');
        types.forEach(type => {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = type;
            typeFilter.appendChild(option);
        });
    })
    .catch(error => console.log('加载学校类型数据失败:', error));

// 省份筛选
document.getElementById('province-filter').addEventListener('change', function() {
    const selectedProvince = this.value;
    const schoolSelect = document.getElementById('school_id');
    const options = schoolSelect.querySelectorAll('option');
    
    options.forEach(option => {
        if (option.value === '') return;
        
        const province = option.getAttribute('data-province');
        if (!selectedProvince || province === selectedProvince) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
});

// 类型筛选
document.getElementById('type-filter').addEventListener('change', function() {
    const selectedType = this.value;
    const schoolSelect = document.getElementById('school_id');
    const options = schoolSelect.querySelectorAll('option');
    
    options.forEach(option => {
        if (option.value === '') return;
        
        const type = option.getAttribute('data-type');
        if (!selectedType || type === selectedType) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
});
</script>
{% endblock %}