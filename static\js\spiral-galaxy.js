// 螺旋星系背景动态效果

// 生成螺旋粒子
function generateSpiralParticles() {
    const container = document.getElementById('spiralParticles');
    if (!container) return;

    const particleCount = 200; // 增加粒子数量

    for (let i = 0; i < particleCount; i++) {
        const angle = (i * 12) % 360; // 调整角度间隔
        const radius = 30 + (i * 2.5); // 调整半径增长
        const x = Math.cos((angle * Math.PI) / 180) * radius;
        const y = Math.sin((angle * Math.PI) / 180) * radius;

        const particle = document.createElement('div');
        particle.className = 'spiral-particle';
        particle.style.left = `calc(50% + ${x}px)`;
        particle.style.top = `calc(50% + ${y}px)`;
        particle.style.animationDelay = `${i * 0.08}s`;
        particle.style.opacity = 0.15 + ((i % 4) * 0.15);

        const colors = ['#06b6d4', '#9333ea', '#ec4899', '#f59e0b', '#10b981', '#8b5cf6'];
        const color = colors[i % 6];
        const glowSize = 1.5 + ((i % 4) + 1);
        particle.style.boxShadow = `0 0 ${glowSize}px ${color}`;

        // 添加随机大小变化
        const size = 0.8 + ((i % 3) * 0.4);
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        container.appendChild(particle);
    }
}

// 生成第二层螺旋粒子
function generateSecondaryParticles() {
    const container = document.getElementById('secondaryParticles');
    if (!container) return;

    const particleCount = 150; // 增加第二层粒子数量

    for (let i = 0; i < particleCount; i++) {
        const angle = (i * 18) % 360; // 调整角度间隔
        const radius = 80 + (i * 4); // 调整半径
        const x = Math.cos((angle * Math.PI) / 180) * radius;
        const y = Math.sin((angle * Math.PI) / 180) * radius;

        const particle = document.createElement('div');
        particle.className = 'secondary-particle';
        particle.style.left = `calc(50% + ${x}px)`;
        particle.style.top = `calc(50% + ${y}px)`;
        particle.style.animationDelay = `${i * 0.12}s`;
        particle.style.opacity = 0.08 + ((i % 5) * 0.12);

        // 添加颜色变化
        const colors = ['#3b82f6', '#9333ea', '#06b6d4', '#ec4899'];
        const color = colors[i % 4];
        particle.style.background = color;
        particle.style.boxShadow = `0 0 2px ${color}`;

        container.appendChild(particle);
    }
}

// 生成浮动星尘
function generateFloatingStardust() {
    const container = document.getElementById('floatingStardust');
    if (!container) return;

    const particleCount = 350; // 大幅增加星尘数量

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'floating-stardust';
        particle.style.left = `${(i * 5.7) % 100}%`;
        particle.style.top = `${(i * 8.3) % 100}%`;
        particle.style.opacity = 0.2 + ((i % 4) * 0.15);
        particle.style.animationDelay = `${(i % 15) * 0.4}s`;
        particle.style.animationDuration = `${8 + ((i % 5) + 1) * 2}s`;

        // 添加随机大小和颜色
        const size = 0.5 + ((i % 3) * 0.3);
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        const colors = ['#ffffff', '#06b6d4', '#9333ea', '#ec4899', '#f59e0b'];
        const color = colors[i % 5];
        particle.style.background = color;
        particle.style.boxShadow = `0 0 ${size * 2}px ${color}`;

        container.appendChild(particle);
    }
}

// 生成流星效果
function generateMeteors() {
    const container = document.getElementById('meteors');
    if (!container) return;

    const meteorCount = 15; // 增加流星数量

    for (let i = 0; i < meteorCount; i++) {
        const meteor = document.createElement('div');
        meteor.className = 'meteor';
        meteor.style.left = `${(i * 11) % 100}%`;
        meteor.style.top = `${(i * 13) % 100}%`;
        meteor.style.animationDuration = `${2 + ((i % 4) + 1)}s`;
        meteor.style.animationDelay = `${(i % 15) * 0.5}s`;

        // 添加不同的流星尾迹长度
        const trailLength = 15 + ((i % 3) * 10);
        meteor.style.width = `${trailLength}px`;
        meteor.style.height = '2px';

        container.appendChild(meteor);
    }
}

// 鼠标跟随效果
function initMouseFollowEffect() {
    const overlay = document.querySelector('.radial-overlay');
    if (!overlay) return;
    
    let mouseX = 50;
    let mouseY = 50;
    
    document.addEventListener('mousemove', (e) => {
        mouseX = (e.clientX / window.innerWidth) * 100;
        mouseY = (e.clientY / window.innerHeight) * 100;
        
        overlay.style.background = `radial-gradient(circle at ${mouseX}% ${mouseY}%, 
            rgba(0,0,0,0) 0%, 
            rgba(30,20,60,0.3) 40%, 
            rgba(0,0,0,0.8) 100%)`;
    });
}

// 响应式调整
function handleResize() {
    const mainSpiral = document.querySelector('.main-spiral');
    const secondarySpiral = document.querySelector('.secondary-spiral');
    
    if (window.innerWidth <= 768) {
        if (mainSpiral) {
            mainSpiral.style.width = '600px';
            mainSpiral.style.height = '600px';
        }
        if (secondarySpiral) {
            secondarySpiral.style.width = '900px';
            secondarySpiral.style.height = '900px';
        }
    } else if (window.innerWidth <= 480) {
        if (mainSpiral) {
            mainSpiral.style.width = '400px';
            mainSpiral.style.height = '400px';
        }
        if (secondarySpiral) {
            secondarySpiral.style.width = '600px';
            secondarySpiral.style.height = '600px';
        }
    } else {
        if (mainSpiral) {
            mainSpiral.style.width = '800px';
            mainSpiral.style.height = '800px';
        }
        if (secondarySpiral) {
            secondarySpiral.style.width = '1200px';
            secondarySpiral.style.height = '1200px';
        }
    }
}

// 初始化所有效果
function initSpiralGalaxy() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                generateSpiralParticles();
                generateSecondaryParticles();
                generateFloatingStardust();
                generateMeteors();
                initMouseFollowEffect();
                handleResize();
            }, 100);
        });
    } else {
        setTimeout(() => {
            generateSpiralParticles();
            generateSecondaryParticles();
            generateFloatingStardust();
            generateMeteors();
            initMouseFollowEffect();
            handleResize();
        }, 100);
    }
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);
}

// 启动螺旋星系效果
initSpiralGalaxy();