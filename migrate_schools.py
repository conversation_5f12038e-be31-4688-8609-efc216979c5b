#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：扩展schools表结构
添加province、city、school_type、level等字段
"""

import sqlite3
from config import Config

def migrate_schools_table():
    """迁移schools表，添加新字段"""
    conn = sqlite3.connect(Config.DATABASE)
    conn.row_factory = sqlite3.Row
    
    try:
        # 检查是否已经存在新字段
        cursor = conn.execute("PRAGMA table_info(schools)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # 添加新字段（如果不存在）
        new_columns = [
            ('province', 'VARCHAR(50)'),
            ('city', 'VARCHAR(50)'),
            ('school_type', 'VARCHAR(50)'),  # 985、211、普通本科、专科等
            ('level', 'VARCHAR(50)'),        # 本科、专科、研究生院等
            ('website', 'VARCHAR(200)'),     # 学校官网
            ('address', 'TEXT'),             # 详细地址
            ('phone', 'VARCHAR(50)'),        # 联系电话
            ('logo_url', 'VARCHAR(200)')     # 学校logo图片URL
        ]
        
        for column_name, column_type in new_columns:
            if column_name not in columns:
                print(f"添加字段: {column_name}")
                conn.execute(f"ALTER TABLE schools ADD COLUMN {column_name} {column_type}")
        
        # 创建新的索引
        new_indexes = [
            'CREATE INDEX IF NOT EXISTS idx_schools_province ON schools(province)',
            'CREATE INDEX IF NOT EXISTS idx_schools_city ON schools(city)',
            'CREATE INDEX IF NOT EXISTS idx_schools_type ON schools(school_type)',
            'CREATE INDEX IF NOT EXISTS idx_schools_level ON schools(level)'
        ]
        
        for index_sql in new_indexes:
            print(f"创建索引: {index_sql}")
            conn.execute(index_sql)
        
        # 更新现有学校数据
        existing_schools_data = [
            (1, '北京', '北京市', '985', '本科', 'https://www.pku.edu.cn', '北京市海淀区颐和园路5号', '010-62751201', ''),
            (2, '北京', '北京市', '985', '本科', 'https://www.tsinghua.edu.cn', '北京市海淀区清华园1号', '010-62793001', ''),
            (3, '上海', '上海市', '985', '本科', 'https://www.fudan.edu.cn', '上海市杨浦区邯郸路220号', '021-65642222', ''),
            (4, '上海', '上海市', '985', '本科', 'https://www.sjtu.edu.cn', '上海市闵行区东川路800号', '021-34200000', '')
        ]
        
        for school_data in existing_schools_data:
            school_id, province, city, school_type, level, website, address, phone, logo_url = school_data
            conn.execute('''
                UPDATE schools 
                SET province = ?, city = ?, school_type = ?, level = ?, 
                    website = ?, address = ?, phone = ?, logo_url = ?
                WHERE id = ?
            ''', (province, city, school_type, level, website, address, phone, logo_url, school_id))
        
        conn.commit()
        print("数据库迁移完成！")
        
    except Exception as e:
        print(f"迁移过程中出现错误: {e}")
        conn.rollback()
    finally:
        conn.close()

def add_more_schools():
    """添加更多学校数据"""
    conn = sqlite3.connect(Config.DATABASE)
    
    # 更多学校数据
    more_schools = [
        ('中国人民大学', '中国人民大学官方论坛板块', '北京', '北京市', '985', '本科', 'https://www.ruc.edu.cn', '北京市海淀区中关村大街59号', '010-62511340', ''),
        ('北京师范大学', '北京师范大学官方论坛板块', '北京', '北京市', '985', '本科', 'https://www.bnu.edu.cn', '北京市海淀区新街口外大街19号', '010-58800001', ''),
        ('华东师范大学', '华东师范大学官方论坛板块', '上海', '上海市', '985', '本科', 'https://www.ecnu.edu.cn', '上海市普陀区中山北路3663号', '021-62232013', ''),
        ('南京大学', '南京大学官方论坛板块', '江苏', '南京市', '985', '本科', 'https://www.nju.edu.cn', '江苏省南京市栖霞区仙林大道163号', '025-89683186', ''),
        ('浙江大学', '浙江大学官方论坛板块', '浙江', '杭州市', '985', '本科', 'https://www.zju.edu.cn', '浙江省杭州市西湖区余杭塘路866号', '0571-87951006', ''),
        ('中山大学', '中山大学官方论坛板块', '广东', '广州市', '985', '本科', 'https://www.sysu.edu.cn', '广东省广州市海珠区新港西路135号', '020-84110000', ''),
        ('华中科技大学', '华中科技大学官方论坛板块', '湖北', '武汉市', '985', '本科', 'https://www.hust.edu.cn', '湖北省武汉市洪山区珞喻路1037号', '027-87542228', ''),
        ('西安交通大学', '西安交通大学官方论坛板块', '陕西', '西安市', '985', '本科', 'https://www.xjtu.edu.cn', '陕西省西安市碑林区咸宁西路28号', '029-82668000', ''),
        ('四川大学', '四川大学官方论坛板块', '四川', '成都市', '985', '本科', 'https://www.scu.edu.cn', '四川省成都市武侯区一环路南一段24号', '028-85400114', ''),
        ('天津大学', '天津大学官方论坛板块', '天津', '天津市', '985', '本科', 'https://www.tju.edu.cn', '天津市南开区卫津路92号', '022-27405486', ''),
        ('大连理工大学', '大连理工大学官方论坛板块', '辽宁', '大连市', '985', '本科', 'https://www.dlut.edu.cn', '辽宁省大连市甘井子区凌工路2号', '0411-84708320', ''),
        ('东北大学', '东北大学官方论坛板块', '辽宁', '沈阳市', '985', '本科', 'https://www.neu.edu.cn', '辽宁省沈阳市和平区文化路3号巷11号', '024-83687392', ''),
        ('吉林大学', '吉林大学官方论坛板块', '吉林', '长春市', '985', '本科', 'https://www.jlu.edu.cn', '吉林省长春市前进大街2699号', '0431-85166114', ''),
        ('哈尔滨工业大学', '哈尔滨工业大学官方论坛板块', '黑龙江', '哈尔滨市', '985', '本科', 'https://www.hit.edu.cn', '黑龙江省哈尔滨市南岗区西大直街92号', '0451-86412114', ''),
        ('山东大学', '山东大学官方论坛板块', '山东', '济南市', '985', '本科', 'https://www.sdu.edu.cn', '山东省济南市历城区山大南路27号', '0531-88364701', ''),
        ('中南大学', '中南大学官方论坛板块', '湖南', '长沙市', '985', '本科', 'https://www.csu.edu.cn', '湖南省长沙市岳麓区麓山南路932号', '0731-88879804', ''),
        ('华南理工大学', '华南理工大学官方论坛板块', '广东', '广州市', '985', '本科', 'https://www.scut.edu.cn', '广东省广州市天河区五山路381号', '020-87110000', ''),
        ('电子科技大学', '电子科技大学官方论坛板块', '四川', '成都市', '985', '本科', 'https://www.uestc.edu.cn', '四川省成都市高新区西源大道2006号', '028-61831137', ''),
        ('西北工业大学', '西北工业大学官方论坛板块', '陕西', '西安市', '985', '本科', 'https://www.nwpu.edu.cn', '陕西省西安市友谊西路127号', '029-88460479', ''),
        ('兰州大学', '兰州大学官方论坛板块', '甘肃', '兰州市', '985', '本科', 'https://www.lzu.edu.cn', '甘肃省兰州市城关区天水南路222号', '0931-8912116', '')
    ]
    
    try:
        for school in more_schools:
            try:
                conn.execute('''
                    INSERT INTO schools (name, description, province, city, school_type, level, website, address, phone, logo_url) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', school)
            except sqlite3.IntegrityError:
                print(f"学校 {school[0]} 已存在，跳过")
        
        conn.commit()
        print(f"成功添加了更多学校数据！")
        
    except Exception as e:
        print(f"添加学校数据时出现错误: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    print("开始数据库迁移...")
    migrate_schools_table()
    print("\n添加更多学校数据...")
    add_more_schools()
    print("\n迁移完成！")