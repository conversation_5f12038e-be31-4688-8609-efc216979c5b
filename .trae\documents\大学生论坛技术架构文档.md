# 大学生论坛技术架构文档

## 1. Architecture design

```mermaid
graph TD
    A[用户浏览器] --> B[Flask Web应用]
    B --> C[SQLite数据库]
    B --> D[云存储服务]
    B --> E[静态文件服务]
    
    subgraph "前端层"
        F[HTML/CSS/JavaScript]
        G[Jinja2模板]
    end
    
    subgraph "后端层"
        B
        H[Flask-Login]
        I[Werkzeug Security]
    end
    
    subgraph "数据层"
        C
    end
    
    subgraph "外部服务"
        D
    end
    
    A --> F
    B --> G
```

## 2. Technology Description

* 前端: HTML5 + CSS3 + JavaScript + Jinja2模板引擎

* 后端: Python Flask\@2.3 + Flask-Login\@0.6 + Werkzeug\@2.3

* 数据库: SQLite3

* 文件存储: 七牛云/阿里云OSS

* 开发工具: Python\@3.9+

## 3. Route definitions

| Route                 | Purpose         |
| --------------------- | --------------- |
| /                     | 首页，显示学校列表和热门帖子  |
| /school/\<school\_id> | 学校板块页，显示该校帖子列表  |
| /post/\<post\_id>     | 帖子详情页，显示帖子内容和评论 |
| /create\_post         | 发帖页面，创建新帖子      |
| /register             | 用户注册页面          |
| /login                | 用户登录页面          |
| /logout               | 用户登出            |
| /profile              | 个人中心页面          |
| /admin                | 管理后台页面          |
| /api/upload\_image    | 图片上传API接口       |
| /api/comment          | 评论相关API接口       |

## 4. API definitions

### 4.1 Core API

用户认证相关

```
POST /api/auth/register
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| username   | string     | true       | 用户名         |
| password   | string     | true       | 密码          |
| email      | string     | true       | 邮箱地址        |
| school\_id | integer    | true       | 所属学校ID      |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 注册是否成功      |
| message    | string     | 返回消息        |
| user\_id   | integer    | 用户ID（成功时返回） |

```
POST /api/auth/login
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| username   | string     | true       | 用户名或邮箱      |
| password   | string     | true       | 密码          |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 登录是否成功      |
| message    | string     | 返回消息        |

帖子相关

```
POST /api/posts
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| title      | string     | true       | 帖子标题        |
| content    | string     | true       | 帖子内容        |
| images     | array      | false      | 图片URL数组     |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 发帖是否成功      |
| post\_id   | integer    | 帖子ID        |

```
GET /api/posts/<school_id>
```

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| posts      | array      | 帖子列表        |
| total      | integer    | 帖子总数        |

图片上传

```
POST /api/upload
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| file       | file       | true       | 图片文件        |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 上传是否成功      |
| url        | string     | 图片访问URL     |

Example:

```json
{
  "username": "student123",
  "password": "password123",
  "email": "<EMAIL>",
  "school_id": 1
}
```

## 5. Server architecture diagram

```mermaid
graph TD
    A[客户端请求] --> B[路由层 Routes]
    B --> C[控制器层 Controllers]
    C --> D[服务层 Services]
    D --> E[数据访问层 Models]
    E --> F[(SQLite数据库)]
    
    C --> G[认证中间件]
    C --> H[文件上传服务]
    H --> I[云存储API]
    
    subgraph Flask应用
        B
        C
        D
        E
        G
        H
    end
```

## 6. Data model

### 6.1 Data model definition

```mermaid
erDiagram
    SCHOOLS ||--o{ USERS : belongs_to
    USERS ||--o{ POSTS : creates
    POSTS ||--o{ COMMENTS : has
    USERS ||--o{ COMMENTS : writes
    
    SCHOOLS {
        int id PK
        string name
        string description
        datetime created_at
    }
    
    USERS {
        int id PK
        string username
        string email
        string password_hash
        int school_id FK
        boolean is_admin
        datetime created_at
        datetime updated_at
    }
    
    POSTS {
        int id PK
        string title
        text content
        int user_id FK
        int school_id FK
        string images
        int view_count
        int comment_count
        datetime created_at
        datetime updated_at
    }
    
    COMMENTS {
        int id PK
        text content
        int post_id FK
        int user_id FK
        int parent_id FK
        datetime created_at
    }
```

### 6.2 Data Definition Language

学校表 (schools)

```sql
-- 创建学校表
CREATE TABLE schools (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_schools_name ON schools(name);

-- 初始化数据
INSERT INTO schools (name, description) VALUES 
('北京大学', '北京大学官方论坛板块'),
('清华大学', '清华大学官方论坛板块'),
('复旦大学', '复旦大学官方论坛板块'),
('上海交通大学', '上海交通大学官方论坛板块');
```

用户表 (users)

```sql
-- 创建用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    school_id INTEGER NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (school_id) REFERENCES schools(id)
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_school_id ON users(school_id);
```

帖子表 (posts)

```sql
-- 创建帖子表
CREATE TABLE posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    user_id INTEGER NOT NULL,
    school_id INTEGER NOT NULL,
    images TEXT, -- JSON格式存储图片URL数组
    view_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (school_id) REFERENCES schools(id)
);

-- 创建索引
CREATE INDEX idx_posts_user_id ON posts(user_id);
CREATE INDEX idx_posts_school_id ON posts(school_id);
CREATE INDEX idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX idx_posts_view_count ON posts(view_count DESC);
```

评论表 (comments)

```sql
-- 创建评论表
CREATE TABLE comments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content TEXT NOT NULL,
    post_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    parent_id INTEGER, -- 用于回复功能，NULL表示顶级评论
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (parent_id) REFERENCES comments(id)
);

-- 创建索引
CREATE INDEX idx_comments_post_id ON comments(post_id);
CREATE INDEX idx_comments_user_id ON comments(user_id);
CREATE INDEX idx_comments_parent
```

