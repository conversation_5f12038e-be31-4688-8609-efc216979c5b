// 大学生论坛 - 主要JavaScript文件

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    initSchoolSelector();
});

// 初始化应用
function initializeApp() {
    // 初始化消息提示自动关闭
    initFlashMessages();
    
    // 初始化搜索功能
    initSearchFeatures();
    
    // 初始化响应式导航
    initResponsiveNav();
    
    // 初始化滚动效果
    initScrollEffects();
}

// 消息提示功能
function initFlashMessages() {
    const flashMessages = document.querySelectorAll('.flash-message');
    
    flashMessages.forEach(message => {
        // 自动关闭消息（5秒后）
        setTimeout(() => {
            if (message.parentElement) {
                message.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    message.remove();
                }, 300);
            }
        }, 5000);
        
        // 点击关闭按钮
        const closeBtn = message.querySelector('.flash-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                message.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    message.remove();
                }, 300);
            });
        }
    });
}

// 搜索功能增强
function initSearchFeatures() {
    const searchForms = document.querySelectorAll('form[action*="search"]');
    
    searchForms.forEach(form => {
        const searchInput = form.querySelector('input[name="q"]');
        if (searchInput) {
            // 搜索建议功能（简单版本）
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();
                
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        // 这里可以添加搜索建议功能
                        console.log('搜索建议:', query);
                    }, 300);
                }
            });
            
            // 回车搜索
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    if (this.value.trim()) {
                        form.submit();
                    }
                }
            });
        }
    });
}

// 响应式导航
function initResponsiveNav() {
    // 移动端导航切换（如果需要）
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }
}

// 滚动效果
function initScrollEffects() {
    // 返回顶部按钮
    createBackToTopButton();
    
    // 导航栏滚动效果
    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // 导航栏阴影效果
        if (scrollTop > 10) {
            navbar.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
        } else {
            navbar.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        }
        
        lastScrollTop = scrollTop;
    });
}

// 创建返回顶部按钮
function createBackToTopButton() {
    const backToTop = document.createElement('button');
    backToTop.innerHTML = '<i class="fas fa-chevron-up"></i>';
    backToTop.className = 'back-to-top';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 80px;
        right: 20px;
        width: 50px;
        height: 50px;
        background: #2196F3;
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: none;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
        transition: all 0.3s;
        z-index: 999;
    `;
    
    backToTop.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    backToTop.addEventListener('mouseenter', () => {
        backToTop.style.background = '#1976D2';
        backToTop.style.transform = 'scale(1.1)';
    });
    
    backToTop.addEventListener('mouseleave', () => {
        backToTop.style.background = '#2196F3';
        backToTop.style.transform = 'scale(1)';
    });
    
    document.body.appendChild(backToTop);
    
    // 滚动显示/隐藏
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.display = 'flex';
        } else {
            backToTop.style.display = 'none';
        }
    });
}

// 工具函数
const Utils = {
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // 格式化时间
    formatTime: function(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return date.toLocaleDateString('zh-CN');
    },
    
    // 显示加载状态
    showLoading: function(element, text = '加载中...') {
        const originalContent = element.innerHTML;
        element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
        element.disabled = true;
        
        return () => {
            element.innerHTML = originalContent;
            element.disabled = false;
        };
    },
    
    // 显示成功消息
    showSuccess: function(message) {
        this.showMessage(message, 'success');
    },
    
    // 显示错误消息
    showError: function(message) {
        this.showMessage(message, 'error');
    },
    
    // 显示消息
    showMessage: function(message, type = 'success') {
        const flashContainer = document.querySelector('.flash-messages') || this.createFlashContainer();
        
        const flashMessage = document.createElement('div');
        flashMessage.className = `flash-message flash-${type}`;
        flashMessage.innerHTML = `
            <span>${message}</span>
            <button class="flash-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        flashContainer.appendChild(flashMessage);
        
        // 自动关闭
        setTimeout(() => {
            if (flashMessage.parentElement) {
                flashMessage.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    flashMessage.remove();
                }, 300);
            }
        }, 5000);
        
        // 点击关闭
        flashMessage.querySelector('.flash-close').addEventListener('click', () => {
            flashMessage.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                flashMessage.remove();
            }, 300);
        });
    },
    
    // 创建消息容器
    createFlashContainer: function() {
        const container = document.createElement('div');
        container.className = 'flash-messages';
        container.style.cssText = `
            position: fixed;
            top: 70px;
            right: 20px;
            z-index: 1001;
            max-width: 400px;
        `;
        document.body.appendChild(container);
        return container;
    }
};

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .loading {
        pointer-events: none;
        opacity: 0.7;
    }
    
    @media (max-width: 768px) {
        .flash-messages {
            right: 15px !important;
            left: 15px !important;
            max-width: none !important;
        }
        
        .back-to-top {
            bottom: 90px !important;
        }
    }
`;
document.head.appendChild(style);

// 显示消息提示
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// 初始化学校选择器
function initSchoolSelector() {
    const schoolSearch = document.getElementById('school-search');
    const provinceFilter = document.getElementById('province-filter');
    const typeFilter = document.getElementById('type-filter');
    const schoolSelect = document.getElementById('school_id');
    const schoolInfo = document.getElementById('school-info');
    
    if (!schoolSelect) return; // 如果不在注册页面，直接返回
    
    // 获取所有学校选项
    const allOptions = Array.from(schoolSelect.options).slice(1); // 排除第一个空选项
    
    // 初始化筛选选项
    initFilterOptions();
    
    // 搜索功能
    if (schoolSearch) {
        schoolSearch.addEventListener('input', function() {
            filterSchools();
        });
    }
    
    // 省份筛选
    if (provinceFilter) {
        provinceFilter.addEventListener('change', function() {
            filterSchools();
        });
    }
    
    // 类型筛选
    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            filterSchools();
        });
    }
    
    // 学校选择变化
    schoolSelect.addEventListener('change', function() {
        showSchoolInfo();
    });
    
    // 初始化筛选选项
    function initFilterOptions() {
        const provinces = new Set();
        const types = new Set();
        
        allOptions.forEach(option => {
            const province = option.dataset.province;
            const type = option.dataset.type;
            
            if (province) provinces.add(province);
            if (type) types.add(type);
        });
        
        // 填充省份选项
        if (provinceFilter) {
            Array.from(provinces).sort().forEach(province => {
                const option = document.createElement('option');
                option.value = province;
                option.textContent = province;
                provinceFilter.appendChild(option);
            });
        }
        
        // 填充类型选项
        if (typeFilter) {
            Array.from(types).sort().forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                typeFilter.appendChild(option);
            });
        }
    }
    
    // 筛选学校
    function filterSchools() {
        const searchTerm = schoolSearch ? schoolSearch.value.toLowerCase() : '';
        const selectedProvince = provinceFilter ? provinceFilter.value : '';
        const selectedType = typeFilter ? typeFilter.value : '';
        
        // 清空当前选项（保留第一个空选项）
        schoolSelect.innerHTML = '<option value="">请选择你的学校</option>';
        
        // 筛选并添加匹配的选项
        const filteredOptions = allOptions.filter(option => {
            const name = option.textContent.toLowerCase();
            const province = option.dataset.province || '';
            const type = option.dataset.type || '';
            
            const matchesSearch = !searchTerm || name.includes(searchTerm);
            const matchesProvince = !selectedProvince || province === selectedProvince;
            const matchesType = !selectedType || type === selectedType;
            
            return matchesSearch && matchesProvince && matchesType;
        });
        
        // 添加筛选后的选项
        filteredOptions.forEach(option => {
            schoolSelect.appendChild(option.cloneNode(true));
        });
        
        // 如果只有一个结果，自动选择
        if (filteredOptions.length === 1) {
            schoolSelect.selectedIndex = 1;
            showSchoolInfo();
        }
    }
    
    // 显示学校信息
    function showSchoolInfo() {
        const selectedOption = schoolSelect.options[schoolSelect.selectedIndex];
        
        if (!selectedOption || !selectedOption.value) {
            if (schoolInfo) schoolInfo.style.display = 'none';
            return;
        }
        
        if (schoolInfo) {
            const nameEl = document.getElementById('selected-school-name');
            const locationEl = document.getElementById('selected-school-location');
            const typeEl = document.getElementById('selected-school-type');
            
            if (nameEl) {
                nameEl.textContent = selectedOption.textContent.split(' - ')[0].split(' (')[0];
            }
            
            if (locationEl) {
                const province = selectedOption.dataset.province;
                const city = selectedOption.dataset.city;
                locationEl.textContent = province && city ? `📍 ${province} ${city}` : '';
            }
            
            if (typeEl) {
                const type = selectedOption.dataset.type;
                typeEl.textContent = type ? `🏫 ${type}` : '';
            }
            
            schoolInfo.style.display = 'block';
        }
    }
}

// 全局暴露工具函数
window.ForumUtils = Utils;