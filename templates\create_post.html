{% extends "base.html" %}

{% block title %}发布新帖 - 大学生论坛{% endblock %}

{% block content %}
<div class="create-post-container" style="max-width: 800px; margin: 20px auto;">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i class="fas fa-edit"></i> 发布新帖
            </h2>
            <p style="color: #666; margin: 0;">在你的学校论坛板块分享想法和讨论</p>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ url_for('create_post') }}" id="post-form">
                <div class="form-group">
                    <label for="title" class="form-label">
                        <i class="fas fa-heading"></i> 帖子标题 *
                    </label>
                    <input type="text" id="title" name="title" class="form-control" 
                           placeholder="请输入帖子标题" required maxlength="200">
                    <small style="color: #666; font-size: 12px;">标题应简洁明了，最多200个字符</small>
                </div>
                
                <div class="form-group">
                    <label for="content" class="form-label">
                        <i class="fas fa-align-left"></i> 帖子内容 *
                    </label>
                    <textarea id="content" name="content" class="form-control" rows="12" 
                              placeholder="请输入帖子内容..." required></textarea>
                    <small style="color: #666; font-size: 12px;">详细描述你的想法，支持换行</small>
                </div>
                
                <!-- 图片上传区域 (预留，阶段四实现) -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-image"></i> 图片上传 (即将开放)
                    </label>
                    <div class="upload-area" style="border: 2px dashed #ddd; border-radius: 8px; padding: 40px; text-align: center; background: #f9f9f9;">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #ccc; margin-bottom: 15px;"></i>
                        <p style="color: #999; margin: 0;">图片上传功能将在后续版本中开放</p>
                    </div>
                </div>
                
                <!-- 发帖须知 -->
                <div class="posting-rules" style="background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 15px; font-size: 16px;">
                        <i class="fas fa-info-circle"></i> 发帖须知
                    </h4>
                    <ul style="color: #666; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                        <li>请确保内容真实、准确，避免传播虚假信息</li>
                        <li>尊重他人，使用文明用语，禁止人身攻击</li>
                        <li>不得发布违法违规、涉及政治敏感的内容</li>
                        <li>鼓励原创内容，转载请注明出处</li>
                        <li>帖子发布后可以编辑，但请谨慎修改</li>
                    </ul>
                </div>
                
                <!-- 操作按钮 -->
                <div class="form-actions" style="display: flex; gap: 15px; justify-content: flex-end; flex-wrap: wrap;">
                    <button type="button" class="btn btn-outline" onclick="saveDraft()">
                        <i class="fas fa-save"></i> 保存草稿
                    </button>
                    <button type="button" class="btn btn-outline" onclick="previewPost()">
                        <i class="fas fa-eye"></i> 预览
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> 发布帖子
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div id="preview-modal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div class="modal-content" style="background: white; margin: 50px auto; padding: 0; max-width: 800px; border-radius: 8px; max-height: 80vh; overflow-y: auto;">
        <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; color: #333;">
                <i class="fas fa-eye"></i> 帖子预览
            </h3>
            <button onclick="closePreview()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body" style="padding: 20px;">
            <div id="preview-content">
                <!-- 预览内容将在这里显示 -->
            </div>
        </div>
        <div class="modal-footer" style="padding: 20px; border-top: 1px solid #eee; text-align: right;">
            <button onclick="closePreview()" class="btn btn-outline">
                <i class="fas fa-edit"></i> 继续编辑
            </button>
            <button onclick="submitFromPreview()" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i> 确认发布
            </button>
        </div>
    </div>
</div>

<script>
// 自动保存草稿
let autoSaveTimer;

function startAutoSave() {
    autoSaveTimer = setInterval(() => {
        const title = document.getElementById('title').value;
        const content = document.getElementById('content').value;
        
        if (title || content) {
            localStorage.setItem('draft_title', title);
            localStorage.setItem('draft_content', content);
        }
    }, 30000); // 每30秒自动保存
}

// 加载草稿
function loadDraft() {
    const draftTitle = localStorage.getItem('draft_title');
    const draftContent = localStorage.getItem('draft_content');
    
    if (draftTitle) {
        document.getElementById('title').value = draftTitle;
    }
    if (draftContent) {
        document.getElementById('content').value = draftContent;
    }
    
    if (draftTitle || draftContent) {
        if (confirm('检测到未发布的草稿，是否恢复？')) {
            // 草稿已加载
        } else {
            // 清除草稿
            localStorage.removeItem('draft_title');
            localStorage.removeItem('draft_content');
            document.getElementById('title').value = '';
            document.getElementById('content').value = '';
        }
    }
}

// 手动保存草稿
function saveDraft() {
    const title = document.getElementById('title').value;
    const content = document.getElementById('content').value;
    
    if (!title && !content) {
        alert('请先输入内容再保存草稿');
        return;
    }
    
    localStorage.setItem('draft_title', title);
    localStorage.setItem('draft_content', content);
    
    // 显示保存成功提示
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> 已保存';
    btn.style.background = '#4CAF50';
    btn.style.color = 'white';
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.style.background = '';
        btn.style.color = '';
    }, 2000);
}

// 预览帖子
function previewPost() {
    const title = document.getElementById('title').value;
    const content = document.getElementById('content').value;
    
    if (!title || !content) {
        alert('请先填写标题和内容');
        return;
    }
    
    const previewContent = document.getElementById('preview-content');
    previewContent.innerHTML = `
        <div class="post-preview">
            <h1 style="font-size: 24px; margin-bottom: 15px; color: #333;">${title}</h1>
            <div class="post-meta" style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee; color: #666;">
                <span><i class="fas fa-user"></i> {{ current_user.username }}</span>
                <span><i class="fas fa-clock"></i> 刚刚</span>
            </div>
            <div class="post-content" style="font-size: 16px; line-height: 1.8;">
                ${content.replace(/\n/g, '<br>')}
            </div>
        </div>
    `;
    
    document.getElementById('preview-modal').style.display = 'block';
}

// 关闭预览
function closePreview() {
    document.getElementById('preview-modal').style.display = 'none';
}

// 从预览提交
function submitFromPreview() {
    closePreview();
    document.getElementById('post-form').submit();
}

// 表单提交处理
document.getElementById('post-form').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const content = document.getElementById('content').value.trim();
    
    if (!title || !content) {
        e.preventDefault();
        alert('请填写完整的标题和内容');
        return;
    }
    
    // 提交成功后清除草稿
    localStorage.removeItem('draft_title');
    localStorage.removeItem('draft_content');
});

// 页面加载时启动功能
document.addEventListener('DOMContentLoaded', function() {
    loadDraft();
    startAutoSave();
});

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (autoSaveTimer) {
        clearInterval(autoSaveTimer);
    }
});

// 点击模态框背景关闭
document.getElementById('preview-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePreview();
    }
});
</script>

<style>
@media (max-width: 768px) {
    .create-post-container {
        margin: 15px auto;
        padding: 0 15px;
    }
    
    .form-actions {
        justify-content: center !important;
    }
    
    .modal-content {
        margin: 20px !important;
        max-height: 90vh !important;
    }
}
</style>
{% endblock %}