#!/usr/bin/env python3
import sqlite3

def check_database():
    conn = sqlite3.connect('forum.db')
    cursor = conn.cursor()
    
    # 检查总学校数量
    cursor.execute('SELECT COUNT(*) FROM schools')
    total_count = cursor.fetchone()[0]
    print(f'总学校数量: {total_count}')
    
    # 检查各省学校数量前10
    cursor.execute('SELECT province, COUNT(*) FROM schools GROUP BY province ORDER BY COUNT(*) DESC LIMIT 10')
    print('\n各省学校数量前10:')
    for row in cursor.fetchall():
        print(f'  {row[0]}: {row[1]}所')
    
    # 检查学校类型分布
    cursor.execute('SELECT school_type, COUNT(*) FROM schools GROUP BY school_type')
    print('\n学校类型分布:')
    for row in cursor.fetchall():
        print(f'  {row[0]}: {row[1]}所')
    
    # 检查一些示例学校数据
    cursor.execute('SELECT name, province, city, school_type, level FROM schools LIMIT 5')
    print('\n示例学校数据:')
    for row in cursor.fetchall():
        print(f'  {row[0]} - {row[1]}{row[2]} - {row[3]} - {row[4]}')
    
    conn.close()

if __name__ == '__main__':
    check_database()