/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
    background-color: #2196F3;
    color: white;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    height: 60px;
}

.nav-brand a {
    color: white;
    text-decoration: none;
    font-size: 20px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-brand a:hover {
    color: #e3f2fd;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-user, .nav-auth {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-text {
    color: #e3f2fd;
    font-size: 14px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
}

.register-btn {
    background-color: #4CAF50;
    border-radius: 20px;
}

.register-btn:hover {
    background-color: #45a049;
}

/* 消息提示样式 */
.flash-messages {
    position: fixed;
    top: 70px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.flash-message {
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    animation: slideIn 0.3s ease-out;
}

.flash-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #4CAF50;
}

.flash-error {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #f44336;
}

.flash-close {
    background: none;
    border: none;
    cursor: pointer;
    color: inherit;
    font-size: 16px;
    padding: 0;
    margin-left: 10px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 主要内容区域 */
.main-content {
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
    min-height: calc(100vh - 140px);
}

/* 首页头部区域样式 */
.hero-section {
    margin-bottom: 40px;
}

.hero-section .card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    text-align: center;
    padding: 40px 20px;
}

.hero-section h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    font-weight: 700;
}

.hero-section p {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.hero-section i {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

/* 各个区块间距优化 */
.schools-section, 
.hot-posts-section, 
.features-section, 
.cta-section {
    margin-bottom: 48px;
}

/* 统一内容间距 */
.content-section {
    margin-bottom: 32px;
}

.content-section:last-child {
    margin-bottom: 0;
}

/* 文本间距优化 */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 16px;
    line-height: 1.3;
}

p {
    margin-bottom: 16px;
}

p:last-child {
    margin-bottom: 0;
}

/* 卡片样式 */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
    padding: 24px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    background: linear-gradient(135deg, rgba(248,249,250,0.8) 0%, rgba(255,255,255,0.6) 100%);
}

/* 卡片头部布局优化 */
.card-header .mb-3 {
    margin-bottom: 20px;
}

.card-header .flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.card-header .info-group {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.card-header .info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: #666;
    background: rgba(255,255,255,0.7);
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.card-header .info-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    background: rgba(255,255,255,0.9);
}

.card-header .info-item i {
    font-size: 12px;
    color: #2196F3;
}

.card-header .location-item {
    color: #2196F3;
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.2);
}

.card-header .count-item {
    color: #666;
    background: rgba(0,0,0,0.03);
}

.card-body {
    padding: 24px;
}

.card-title {
    margin: 0;
    font-size: 22px;
    font-weight: 700;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
    line-height: 1.3;
    margin-bottom: 8px;
}

.card-title i {
    color: #2196F3;
    font-size: 20px;
    background: rgba(33, 150, 243, 0.1);
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.card-title:hover i {
    background: rgba(33, 150, 243, 0.2);
    transform: scale(1.1);
}

/* 响应式布局辅助类 */
.mb-3 {
    margin-bottom: 1rem;
}

.gap-3 {
    gap: 0.75rem;
}

.gap-4 {
    gap: 1rem;
}

.gap-1 {
    gap: 0.25rem;
}

.gap-2 {
    gap: 0.5rem;
}

.flex-wrap {
    flex-wrap: wrap;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .card-header {
        padding: 20px 15px;
    }
    
    .card-header .flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 16px;
    }
    
    .card-header .info-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        width: 100%;
    }
    
    .card-header .info-item {
        padding: 8px 14px;
        font-size: 13px;
    }
    
    .flex-wrap {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .flex-wrap .btn, .card-header .btn {
        margin-top: 10px;
        align-self: stretch;
        justify-content: center;
        padding: 10px 16px;
    }
    
    /* 卡片标题移动端优化 */
    .card-title {
        font-size: 18px;
    }
    
    .card-title i {
        font-size: 16px;
    }
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    text-align: center;
}

.btn-primary {
    background-color: #2196F3;
    color: white;
}

.btn-primary:hover {
    background-color: #1976D2;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 20px;
    font-weight: 500;
}

.btn-success {
    background-color: #4CAF50;
    color: white;
}

.btn-success:hover {
    background-color: #45a049;
    color: white;
}

.btn-outline {
    background-color: transparent;
    color: #2196F3;
    border: 1px solid #2196F3;
}

.btn-outline:hover {
    background-color: #2196F3;
    color: white;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.form-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* 网格布局 */
.grid {
    display: grid;
    gap: 24px;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
}

.grid-5 {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
}

.grid-6 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 学校卡片样式 */
.school-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border: 1px solid #f0f0f0;
    position: relative;
    overflow: hidden;
}

.school-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0,0,0,0.15);
    border-color: #2196F3;
}

.school-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2196F3, #21CBF3);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.school-card:hover::before {
    transform: scaleX(1);
}

.school-card i {
    font-size: 48px;
    color: #2196F3;
    margin-bottom: 20px;
    transition: color 0.3s ease;
}

.school-card:hover i {
    color: #1976D2;
}

.school-card h3 {
    color: #333;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.school-card:hover h3 {
    color: #2196F3;
}

.school-card p {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 12px;
}

.school-card .badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 10px;
    border-radius: 6px;
    margin-top: 8px;
}

/* 帖子列表样式 */
.post-item {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.post-item:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.post-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    text-decoration: none;
}

.post-title:hover {
    color: #2196F3;
}

.post-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.post-content {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
}

.post-stats {
    display: flex;
    align-items: center;
    gap: 20px;
    color: #888;
    font-size: 13px;
}

/* 评论样式 */
.comment-item {
    background: rgba(249, 249, 249, 0.8);
    backdrop-filter: blur(5px);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #2196F3;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.comment-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.comment-content {
    color: #333;
    line-height: 1.6;
}

/* 页脚样式 */
.footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: 40px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 学校选择器样式 */
.school-selector {
    position: relative;
}

.search-box {
    position: relative;
    margin-bottom: 10px;
}

.search-box .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    pointer-events: none;
}

.filter-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 10px;
}

.school-info {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #2196F3;
}

.school-details h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
}

.school-details p {
    margin: 4px 0;
    color: #666;
    font-size: 14px;
}

/* 学校列表页面样式 */
.schools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.school-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

.school-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #2196F3;
}

.school-item h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
}

.school-item .school-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.school-item .meta-item {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;
}

.school-item .meta-item i {
    margin-right: 5px;
    width: 16px;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 12px;
    text-transform: uppercase;
}

.badge-primary { background: #e3f2fd; color: #1976d2; }
.badge-danger { background: #ffebee; color: #d32f2f; }
.badge-warning { background: #fff3e0; color: #f57c00; }
.badge-purple { background: #f3e5f5; color: #7b1fa2; }
.badge-success { background: #e8f5e8; color: #388e3c; }

/* 筛选区域样式 */
.filter-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.filter-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
        flex-direction: column;
        height: auto;
        padding: 10px 15px;
    }
    
    .nav-menu {
        margin-top: 10px;
    }
    
    .nav-user, .nav-auth {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .main-content {
        padding: 0 15px;
        margin: 20px auto;
    }
    
    .flash-messages {
        right: 15px;
        left: 15px;
        max-width: none;
    }
    
    /* 移动端首页头部优化 */
    .hero-section .card {
        padding: 30px 15px;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .hero-section i {
        font-size: 2.5rem;
    }
    
    /* 移动端网格布局 */
    .grid-2, .grid-3, .grid-4, .grid-5, .grid-6 {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .school-card {
        min-height: 160px;
        padding: 15px;
    }
    
    .school-card i {
        font-size: 36px;
    }
    
    .school-card h3 {
        font-size: 14px;
    }
    
    .post-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .post-stats {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .filter-options {
        grid-template-columns: 1fr;
    }
    
    .filter-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .schools-grid {
        grid-template-columns: 1fr;
    }
    
    /* 区块间距移动端优化 */
    .schools-section, 
    .hot-posts-section, 
    .features-section, 
    .cta-section {
        margin-bottom: 30px;
    }
}

@media (max-width: 480px) {
    .nav-brand a {
        font-size: 18px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    /* 小屏幕单列布局 */
    .grid-2, .grid-3, .grid-4, .grid-5, .grid-6 {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .school-card {
        padding: 20px 15px;
        min-height: 140px;
    }
    
    .school-card i {
        font-size: 42px;
    }
    
    .school-card h3 {
        font-size: 15px;
    }
    
    .post-item {
        padding: 15px;
    }
    
    /* 首页头部小屏幕优化 */
    .hero-section h1 {
        font-size: 1.8rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mt-20 { margin-top: 20px; }
.mb-20 { margin-bottom: 20px; }
.p-20 { padding: 20px; }
.hidden { display: none; }
.flex { display: flex; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.gap-10 { gap: 10px; }
.gap-20 { gap: 20px; }

/* 文本样式类 */
.section-subtitle {
    color: #666;
    margin: 0;
    margin-top: 5px;
    font-size: 14px;
    line-height: 1.5;
}

.cta-subtitle {
    margin-bottom: 20px;
    opacity: 0.9;
    font-size: 16px;
    line-height: 1.6;
}